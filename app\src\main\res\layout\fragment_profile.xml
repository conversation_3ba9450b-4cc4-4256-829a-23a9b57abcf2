<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/header_gradient"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="👤 Hồ sơ"
                android:textColor="@android:color/white"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="Quản lý thông tin cá nhân"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:alpha="0.9" />

        </LinearLayout>

        <!-- Logged In Layout -->
        <LinearLayout
            android:id="@+id/layout_logged_in"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <!-- User Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <!-- Avatar -->
                    <FrameLayout
                        android:layout_width="80dp"
                        android:layout_height="80dp">

                        <ImageView
                            android:id="@+id/iv_user_avatar"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/circle_background"
                            android:scaleType="centerCrop"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/tv_user_initials"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/circle_background"
                            android:gravity="center"
                            android:text="AB"
                            android:textColor="@android:color/white"
                            android:textSize="24sp"
                            android:textStyle="bold" />

                    </FrameLayout>

                    <!-- User Info -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_user_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Nguyễn Văn A"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="#333333" />

                        <TextView
                            android:id="@+id/tv_user_email"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="<EMAIL>"
                            android:textSize="14sp"
                            android:textColor="#666666" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Menu Items -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp">

                <!-- Statistics -->
                <LinearLayout
                    android:id="@+id/menu_statistics"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_statistics"
                        android:tint="#2196F3" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:text="Thống kê"
                        android:textSize="16sp"
                        android:textColor="#333333" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="#666666" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#E0E0E0"
                    android:layout_marginHorizontal="16dp" />

                <!-- Settings -->
                <LinearLayout
                    android:id="@+id/menu_settings"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_settings"
                        android:tint="#2196F3" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:text="Cài đặt"
                        android:textSize="16sp"
                        android:textColor="#333333" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="#666666" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#E0E0E0"
                    android:layout_marginHorizontal="16dp" />

                <!-- Help -->
                <LinearLayout
                    android:id="@+id/menu_help"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_help"
                        android:tint="#2196F3" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:text="Trợ giúp"
                        android:textSize="16sp"
                        android:textColor="#333333" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="#666666" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#E0E0E0"
                    android:layout_marginHorizontal="16dp" />

                <!-- About -->
                <LinearLayout
                    android:id="@+id/menu_about"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_info"
                        android:tint="#2196F3" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:text="Về ứng dụng"
                        android:textSize="16sp"
                        android:textColor="#333333" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="#666666" />

                </LinearLayout>

                <!-- Logout Button -->
                <Button
                    android:id="@+id/btn_logout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:background="@drawable/button_outline"
                    android:text="Đăng xuất"
                    android:textColor="#F44336"
                    android:textSize="16sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Not Logged In Layout -->
        <LinearLayout
            android:id="@+id/layout_not_logged_in"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="32dp"
            android:gravity="center">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:src="@drawable/ic_login_illustration"
                android:tint="#2196F3"
                android:layout_marginBottom="24dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Chưa đăng nhập"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Đăng nhập để đồng bộ dữ liệu và truy cập đầy đủ tính năng"
                android:textSize="16sp"
                android:textColor="#666666"
                android:textAlignment="center"
                android:layout_marginBottom="32dp" />

            <Button
                android:id="@+id/btn_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/button_primary"
                android:text="Đăng nhập"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>

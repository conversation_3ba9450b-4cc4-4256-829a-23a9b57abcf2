// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentRegisterBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton btnRegister;

  @NonNull
  public final CheckBox cbTerms;

  @NonNull
  public final TextInputEditText etConfirmPassword;

  @NonNull
  public final TextInputEditText etEmail;

  @NonNull
  public final TextInputEditText etFullName;

  @NonNull
  public final TextInputEditText etPassword;

  @NonNull
  public final TextInputEditText etUsername;

  @NonNull
  public final ProgressBar progressBar;

  private FragmentRegisterBinding(@NonNull ScrollView rootView, @NonNull MaterialButton btnRegister,
      @NonNull CheckBox cbTerms, @NonNull TextInputEditText etConfirmPassword,
      @NonNull TextInputEditText etEmail, @NonNull TextInputEditText etFullName,
      @NonNull TextInputEditText etPassword, @NonNull TextInputEditText etUsername,
      @NonNull ProgressBar progressBar) {
    this.rootView = rootView;
    this.btnRegister = btnRegister;
    this.cbTerms = cbTerms;
    this.etConfirmPassword = etConfirmPassword;
    this.etEmail = etEmail;
    this.etFullName = etFullName;
    this.etPassword = etPassword;
    this.etUsername = etUsername;
    this.progressBar = progressBar;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentRegisterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentRegisterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_register, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentRegisterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_register;
      MaterialButton btnRegister = ViewBindings.findChildViewById(rootView, id);
      if (btnRegister == null) {
        break missingId;
      }

      id = R.id.cb_terms;
      CheckBox cbTerms = ViewBindings.findChildViewById(rootView, id);
      if (cbTerms == null) {
        break missingId;
      }

      id = R.id.et_confirm_password;
      TextInputEditText etConfirmPassword = ViewBindings.findChildViewById(rootView, id);
      if (etConfirmPassword == null) {
        break missingId;
      }

      id = R.id.et_email;
      TextInputEditText etEmail = ViewBindings.findChildViewById(rootView, id);
      if (etEmail == null) {
        break missingId;
      }

      id = R.id.et_full_name;
      TextInputEditText etFullName = ViewBindings.findChildViewById(rootView, id);
      if (etFullName == null) {
        break missingId;
      }

      id = R.id.et_password;
      TextInputEditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.et_username;
      TextInputEditText etUsername = ViewBindings.findChildViewById(rootView, id);
      if (etUsername == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      return new FragmentRegisterBinding((ScrollView) rootView, btnRegister, cbTerms,
          etConfirmPassword, etEmail, etFullName, etPassword, etUsername, progressBar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

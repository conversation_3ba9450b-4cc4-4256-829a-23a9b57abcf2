package com.example.myapplication.fragments;

import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.text.Editable;
import android.text.TextWatcher;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapplication.R;
import com.example.myapplication.adapters.TaskAdapter;
import com.example.myapplication.adapters.CategoryAdapter;
import com.example.myapplication.adapters.TaskStepEditAdapter;
import com.example.myapplication.adapters.TaskStepDetailAdapter;
import com.example.myapplication.auth.AuthManager;
import com.example.myapplication.database.TodoDatabase;
import com.example.myapplication.database.entities.CategoryEntity;
import com.example.myapplication.database.entities.TaskEntity;
import com.example.myapplication.database.entities.TaskStepEntity;
import com.example.myapplication.models.Task;
import com.example.myapplication.models.Category;
import com.example.myapplication.models.TaskStep;
import com.example.myapplication.viewmodel.TodoViewModel;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.chip.Chip;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

public class TaskListFragment extends Fragment {

    private RecyclerView recyclerViewTasks;
    private TaskAdapter taskAdapter;
    private FloatingActionButton fabAddTask;
    private Spinner spinnerCategoryFilter;
    private Button btnAddCategory;
    private CheckBox cbShowIncompleteOnly;
    private EditText etSearch;

    private List<Task> taskList;
    private List<Category> categoryList;
    private String selectedCategoryId = null;
    private String searchQuery = "";
    private boolean showIncompleteOnly = false;

    // Room Database components
    private TodoViewModel todoViewModel;
    private AuthManager authManager;
    private List<TaskEntity> taskEntityList;
    private List<CategoryEntity> categoryEntityList;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_task_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Initialize ViewModel and AuthManager
        todoViewModel = new ViewModelProvider(this).get(TodoViewModel.class);
        authManager = AuthManager.getInstance(requireContext());

        initViews(view);
        setupData();
        setupRecyclerViews();
        setupFab();
        setupCategorySpinner();
        setupAddCategoryButton();
        setupCompletionFilter();
        setupSearch();
        observeData();
    }

    private void initViews(View view) {
        recyclerViewTasks = view.findViewById(R.id.recycler_view_tasks);
        fabAddTask = view.findViewById(R.id.fab_add_task);
        spinnerCategoryFilter = view.findViewById(R.id.spinner_category_filter);
        btnAddCategory = view.findViewById(R.id.btn_add_category);
        cbShowIncompleteOnly = view.findViewById(R.id.cb_show_incomplete_only);
        etSearch = view.findViewById(R.id.et_search);
    }

    private void setupData() {
        // Initialize categories
        categoryList = new ArrayList<>();
        Category[] defaultCategories = Category.getDefaultCategories();
        for (Category category : defaultCategories) {
            categoryList.add(category);
        }

        // Initialize sample tasks
        taskList = new ArrayList<>();
        taskList.add(new Task(UUID.randomUUID().toString(), "Hoàn thành báo cáo", "Viết báo cáo tháng cho dự án", categoryList.get(1), new Date(System.currentTimeMillis() + 86400000), Task.Priority.HIGH));
        taskList.add(new Task(UUID.randomUUID().toString(), "Mua sắm cuối tuần", "Mua thực phẩm và đồ dùng cần thiết", categoryList.get(4), new Date(System.currentTimeMillis() + 172800000), Task.Priority.MEDIUM));
        taskList.add(new Task(UUID.randomUUID().toString(), "Tập thể dục", "Chạy bộ 30 phút trong công viên", categoryList.get(3), new Date(System.currentTimeMillis() + 3600000), Task.Priority.LOW));
        taskList.add(new Task(UUID.randomUUID().toString(), "Học tiếng Anh", "Ôn tập từ vựng và ngữ pháp", categoryList.get(2), new Date(System.currentTimeMillis() + 7200000), Task.Priority.MEDIUM));
    }

    private void setupRecyclerViews() {
        // Setup tasks RecyclerView
        taskAdapter = new TaskAdapter(taskList);
        recyclerViewTasks.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerViewTasks.setAdapter(taskAdapter);

        taskAdapter.setOnTaskClickListener(new TaskAdapter.OnTaskClickListener() {
            @Override
            public void onTaskClick(Task task) {
                // Open task detail dialog
                showTaskDetailDialog(task);
            }

            @Override
            public void onTaskToggle(Task task, boolean isCompleted) {
                // Update task completion status in database
                if (task.getId() != null) {
                    // Update task object immediately to keep UI consistent
                    task.setCompleted(isCompleted);

                    // Update the specific task in adapter
                    taskAdapter.updateTask(task);

                    TodoDatabase.databaseWriteExecutor.execute(() -> {
                        try {
                            int taskId = Integer.parseInt(task.getId());
                            TaskEntity taskEntity = new TaskEntity();
                            taskEntity.setId(taskId);
                            taskEntity.setTitle(task.getTitle());
                            taskEntity.setDescription(task.getDescription());
                            taskEntity.setCategoryId(Integer.parseInt(task.getCategory().getId()));
                            taskEntity.setPriority(task.getPriority());
                            taskEntity.setCompleted(isCompleted);
                            taskEntity.setDueDate(task.getDueDate());
                            taskEntity.setCreatedAt(task.getCreatedAt());
                            taskEntity.setUserId(authManager.getCurrentUserId()); // QUAN TRỌNG: Thêm userId để task không bị mất

                            todoViewModel.getRepository().updateTaskSync(taskEntity);

                            // Show toast on main thread
                            if (getActivity() != null) {
                                getActivity().runOnUiThread(() -> {
                                    Toast.makeText(getContext(), isCompleted ? "Đã hoàn thành!" : "Chưa hoàn thành", Toast.LENGTH_SHORT).show();
                                });
                            }
                        } catch (NumberFormatException e) {
                            // Handle invalid ID - revert the change
                            if (getActivity() != null) {
                                getActivity().runOnUiThread(() -> {
                                    task.setCompleted(!isCompleted);
                                    taskAdapter.updateTask(task);
                                    Toast.makeText(getContext(), "Lỗi cập nhật nhiệm vụ", Toast.LENGTH_SHORT).show();
                                });
                            }
                        }
                    });
                }
            }
        });


    }

    private void setupFab() {
        fabAddTask.setOnClickListener(v -> showAddTaskDialog());
    }

    private void setupAddCategoryButton() {
        btnAddCategory.setOnClickListener(v -> showAddCategoryDialog());
    }

    private void setupCompletionFilter() {
        cbShowIncompleteOnly.setOnCheckedChangeListener((buttonView, isChecked) -> {
            showIncompleteOnly = isChecked;
            filterTasks();
        });
    }

    private void setupSearch() {
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                searchQuery = s.toString().trim();
                filterTasks();
            }
        });
    }

    private void setupCategorySpinner() {
        // Create category list with "All" option
        List<Category> spinnerCategories = new ArrayList<>();
        spinnerCategories.add(new Category("all", "Tất cả", "#2196F3", "📋"));

        if (categoryList != null) {
            spinnerCategories.addAll(categoryList);
        }

        // Create custom adapter for spinner
        ArrayAdapter<Category> spinnerAdapter = new ArrayAdapter<Category>(getContext(), android.R.layout.simple_spinner_item, spinnerCategories) {
            @Override
            public View getView(int position, View convertView, ViewGroup parent) {
                TextView view = (TextView) super.getView(position, convertView, parent);
                Category category = getItem(position);
                if (category != null) {
                    view.setText(category.getIcon() + " " + category.getName());
                }
                return view;
            }

            @Override
            public View getDropDownView(int position, View convertView, ViewGroup parent) {
                TextView view = (TextView) super.getDropDownView(position, convertView, parent);
                Category category = getItem(position);
                if (category != null) {
                    view.setText(category.getIcon() + " " + category.getName());
                }
                return view;
            }
        };

        spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCategoryFilter.setAdapter(spinnerAdapter);

        // Set selection listener
        spinnerCategoryFilter.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                Category selectedCategory = (Category) parent.getItemAtPosition(position);
                if (selectedCategory.getId().equals("all")) {
                    selectedCategoryId = null;
                } else {
                    selectedCategoryId = selectedCategory.getId();
                }
                filterTasks();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedCategoryId = null;
                filterTasks();
            }
        });
    }
// sắp xếp
    private void filterTasks() {
        if (taskList == null || taskAdapter == null) return;

        List<Task> filteredTasks = new ArrayList<>();
        for (Task task : taskList) {
            boolean matchesCategory = selectedCategoryId == null ||
                (task.getCategory() != null && task.getCategory().getId().equals(selectedCategoryId));

            boolean matchesSearch = searchQuery.isEmpty() ||
                task.getTitle().toLowerCase().contains(searchQuery.toLowerCase()) ||
                task.getDescription().toLowerCase().contains(searchQuery.toLowerCase());

            boolean matchesCompletion = !showIncompleteOnly || !task.isCompleted();

            if (matchesCategory && matchesSearch && matchesCompletion) {
                filteredTasks.add(task);
            }
        }

        // Sort tasks: incomplete tasks first, then by priority
        filteredTasks.sort((task1, task2) -> {
            // First sort by completion status (incomplete first)
            if (task1.isCompleted() != task2.isCompleted()) {
                return task1.isCompleted() ? 1 : -1;
            }
            // Then sort by priority (HIGH > MEDIUM > LOW)
            return task2.getPriority().ordinal() - task1.getPriority().ordinal();
        });

        taskAdapter.updateTasks(filteredTasks);
    }

    private void showAddTaskDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_add_task, null);
        
        EditText etTitle = dialogView.findViewById(R.id.et_task_title);
        EditText etDescription = dialogView.findViewById(R.id.et_task_description);
        Spinner spinnerCategory = dialogView.findViewById(R.id.spinner_category);
        Spinner spinnerPriority = dialogView.findViewById(R.id.spinner_priority);
        Button btnAddStep = dialogView.findViewById(R.id.btn_add_step);
        RecyclerView recyclerViewSteps = dialogView.findViewById(R.id.recycler_view_steps);
        TextView tvSelectedDate = dialogView.findViewById(R.id.tv_selected_date);
        Button btnSelectDate = dialogView.findViewById(R.id.btn_select_date);

        // Selected date variable
        final Date[] selectedDueDate = {null};

        // Setup category spinner with real data from database
        List<Category> categories = new ArrayList<>();
        if (categoryEntityList != null && !categoryEntityList.isEmpty()) {
            for (CategoryEntity entity : categoryEntityList) {
                categories.add(new Category(
                    String.valueOf(entity.getId()),
                    entity.getName(),
                    entity.getColor(),
                    entity.getIcon()
                ));
            }
        }
        ArrayAdapter<Category> categoryAdapter = new ArrayAdapter<>(getContext(), android.R.layout.simple_spinner_item, categories);
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCategory.setAdapter(categoryAdapter);

        // Setup priority spinner
        ArrayAdapter<Task.Priority> priorityAdapter = new ArrayAdapter<>(getContext(), android.R.layout.simple_spinner_item, Task.Priority.values());
        priorityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerPriority.setAdapter(priorityAdapter);

        // Setup steps RecyclerView
        List<TaskStep> stepList = new ArrayList<>();
        TaskStepEditAdapter stepAdapter = new TaskStepEditAdapter(stepList, new TaskStepEditAdapter.OnStepActionListener() {
            @Override
            public void onStepDeleted(int position) {
                stepList.remove(position);
                notifyDataSetChanged();
            }

            @Override
            public void onStepChanged(int position, TaskStep step) {
                // Step is already updated in the list
            }

            private void notifyDataSetChanged() {
                // Refresh the adapter
                TaskStepEditAdapter currentAdapter = (TaskStepEditAdapter) recyclerViewSteps.getAdapter();
                if (currentAdapter != null) {
                    currentAdapter.updateSteps(stepList);
                }
            }
        });

        recyclerViewSteps.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerViewSteps.setAdapter(stepAdapter);

        // Add step button click
        btnAddStep.setOnClickListener(v -> {
            TaskStep newStep = new TaskStep();
            newStep.setId(String.valueOf(System.currentTimeMillis()));
            newStep.setTitle("");
            newStep.setDescription("");
            newStep.setOrder(stepList.size());
            stepList.add(newStep);
            stepAdapter.updateSteps(stepList);
        });

        // Date picker setup
        btnSelectDate.setOnClickListener(v -> {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, 1); // Default to tomorrow

            DatePickerDialog datePickerDialog = new DatePickerDialog(
                getContext(),
                (view, year, month, dayOfMonth) -> {
                    Calendar selectedCalendar = Calendar.getInstance();
                    selectedCalendar.set(year, month, dayOfMonth);

                    // Check if selected date is in the future
                    Calendar today = Calendar.getInstance();
                    today.set(Calendar.HOUR_OF_DAY, 0);
                    today.set(Calendar.MINUTE, 0);
                    today.set(Calendar.SECOND, 0);
                    today.set(Calendar.MILLISECOND, 0);

                    if (selectedCalendar.before(today)) {
                        Toast.makeText(getContext(), "Vui lòng chọn ngày trong tương lai", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    selectedDueDate[0] = selectedCalendar.getTime();
                    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
                    tvSelectedDate.setText("📅 " + sdf.format(selectedDueDate[0]));
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
            );

            // Set minimum date to tomorrow
            datePickerDialog.getDatePicker().setMinDate(calendar.getTimeInMillis());
            datePickerDialog.show();
        });

        builder.setView(dialogView)
                .setTitle("Thêm nhiệm vụ mới")
                .setPositiveButton("Thêm", (dialog, which) -> {
                    String title = etTitle.getText().toString().trim();
                    String description = etDescription.getText().toString().trim();
                    Category category = (Category) spinnerCategory.getSelectedItem();
                    Task.Priority priority = (Task.Priority) spinnerPriority.getSelectedItem();

                    if (!title.isEmpty()) {
                        // Check if due date is selected
                        if (selectedDueDate[0] == null) {
                            Toast.makeText(getContext(), "Vui lòng chọn thời gian đến hạn", Toast.LENGTH_SHORT).show();
                            return;
                        }

                        // Filter out empty steps
                        List<TaskStep> validSteps = new ArrayList<>();
                        for (TaskStep step : stepList) {
                            if (step.getTitle() != null && !step.getTitle().trim().isEmpty()) {
                                validSteps.add(step);
                            }
                        }
                        addTaskToDatabase(title, description, category, priority, selectedDueDate[0], validSteps);
                    }
                })
                .setNegativeButton("Hủy", null)
                .show();
    }

    private void observeData() {
        int currentUserId = authManager.getCurrentUserId();

        // Observe tasks from database for current user
        todoViewModel.getTasksByUser(currentUserId).observe(getViewLifecycleOwner(), taskEntities -> {
            if (taskEntities != null) {
                // Log số lượng task được load từ database
                android.util.Log.d("TaskListFragment", "=== LiveData Observer Triggered ===");
                android.util.Log.d("TaskListFragment", "Tasks loaded from DB: " + taskEntities.size() + " tasks");

                // Log chi tiết từng task để debug trạng thái completed
                for (TaskEntity entity : taskEntities) {
                    android.util.Log.d("TaskListFragment", "Task loaded: " + entity.getTitle() +
                        " (ID: " + entity.getId() + ", completed: " + entity.isCompleted() +
                        ", userId: " + entity.getUserId() + ")");
                }

                android.util.Log.d("TaskListFragment", "About to refresh UI with new task data...");

                taskEntityList = taskEntities;
                refreshTasksWithCategories();
            } else {
                android.util.Log.w("TaskListFragment", "taskEntities is null!");
            }
        });

        // Observe categories from database for current user (including default categories)
        todoViewModel.getCategoriesForUser(currentUserId).observe(getViewLifecycleOwner(), categoryEntities -> {
            if (categoryEntities != null) {
                categoryEntityList = categoryEntities;
                // Convert CategoryEntity to Category for existing adapter
                categoryList = new ArrayList<>();
                for (CategoryEntity entity : categoryEntities) {
                    categoryList.add(entity.toCategory());
                }

                // Update spinner
                setupCategorySpinner();
                // Refresh tasks with proper categories
                refreshTasksWithCategories();
            }
        });
    }

    private void refreshTasksWithCategories() {
        if (taskEntityList != null && categoryEntityList != null) {
            taskList = new ArrayList<>();
            for (TaskEntity entity : taskEntityList) {
                // Find the corresponding category
                Category category = null;
                for (CategoryEntity catEntity : categoryEntityList) {
                    if (catEntity.getId() == entity.getCategoryId()) {
                        category = catEntity.toCategory();
                        break;
                    }
                }

                // If no category found, use default
                if (category == null) {
                    category = new Category("0", "Chung", "#2196F3", "📝");
                }

                Task task = entity.toTask(category);
                taskList.add(task);
            }
            // Apply current filters
            filterTasks();
        }
    }

    private void addTaskToDatabase(String title, String description, Category category, Task.Priority priority) {
        // Create TaskEntity directly
        TaskEntity taskEntity = new TaskEntity();
        taskEntity.setTitle(title);
        taskEntity.setDescription(description);
        taskEntity.setDueDate(new Date(System.currentTimeMillis() + 86400000)); // Tomorrow
        taskEntity.setPriority(priority);
        taskEntity.setCompleted(false);
        taskEntity.setCreatedAt(new Date());
        taskEntity.setUserId(authManager.getCurrentUserId());

        // Find category ID, default to 1 if not found
        int categoryId = 1;
        if (category != null) {
            try {
                categoryId = Integer.parseInt(category.getId());
            } catch (NumberFormatException e) {
                // If category ID is not a number, try to find by name
                if (categoryEntityList != null && !categoryEntityList.isEmpty()) {
                    for (CategoryEntity entity : categoryEntityList) {
                        if (entity.getName().equals(category.getName())) {
                            categoryId = entity.getId();
                            break;
                        }
                    }
                }
            }
        }
        taskEntity.setCategoryId(categoryId);

        // Insert to database
        todoViewModel.insertTask(taskEntity);

        Toast.makeText(getContext(), "Đã thêm nhiệm vụ mới!", Toast.LENGTH_SHORT).show();
    }

    private void addTaskToDatabase(String title, String description, Category category, Task.Priority priority, Date dueDate, List<TaskStep> steps) {
        // Create TaskEntity directly
        TaskEntity taskEntity = new TaskEntity();
        taskEntity.setTitle(title);
        taskEntity.setDescription(description);
        taskEntity.setDueDate(dueDate);
        taskEntity.setPriority(priority);
        taskEntity.setCompleted(false);
        taskEntity.setCreatedAt(new Date());
        taskEntity.setUserId(authManager.getCurrentUserId());

        // Find category ID, default to 1 if not found
        int categoryId = 1;
        if (category != null) {
            try {
                categoryId = Integer.parseInt(category.getId());
            } catch (NumberFormatException e) {
                // If category ID is not a number, try to find by name
                if (categoryEntityList != null && !categoryEntityList.isEmpty()) {
                    for (CategoryEntity entity : categoryEntityList) {
                        if (entity.getName().equals(category.getName())) {
                            categoryId = entity.getId();
                            break;
                        }
                    }
                }
            }
        }
        taskEntity.setCategoryId(categoryId);

        // Insert task to database first, then insert steps
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            // Insert task and get the generated ID
            long taskId = todoViewModel.getRepository().insertTaskAndGetId(taskEntity);

            // Insert steps if any
            if (steps != null && !steps.isEmpty()) {
                for (int i = 0; i < steps.size(); i++) {
                    TaskStep step = steps.get(i);
                    if (step.getTitle() != null && !step.getTitle().trim().isEmpty()) {
                        TaskStepEntity stepEntity = new TaskStepEntity();
                        stepEntity.setTaskId((int) taskId);
                        stepEntity.setTitle(step.getTitle());
                        stepEntity.setDescription(step.getDescription());
                        stepEntity.setCompleted(step.isCompleted());
                        stepEntity.setStepOrder(i);
                        stepEntity.setCreatedAt(new Date());

                        todoViewModel.getRepository().insertTaskStepSync(stepEntity);
                    }
                }
            }
        });

        Toast.makeText(getContext(), "Đã thêm nhiệm vụ mới với " + steps.size() + " bước!", Toast.LENGTH_SHORT).show();
    }

    private void showTaskDetailDialog(Task task) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_task_detail, null);

        // Initialize views
        TextView tvTaskTitle = dialogView.findViewById(R.id.tv_task_title);
        TextView tvTaskDescription = dialogView.findViewById(R.id.tv_task_description);
        com.google.android.material.chip.Chip chipCategory = dialogView.findViewById(R.id.chip_category);
        com.google.android.material.chip.Chip chipPriority = dialogView.findViewById(R.id.chip_priority);
        LinearLayout layoutProgress = dialogView.findViewById(R.id.layout_progress);
        ProgressBar progressBar = dialogView.findViewById(R.id.progress_bar);
        TextView tvProgressText = dialogView.findViewById(R.id.tv_progress_text);
        Button btnAddStepDetail = dialogView.findViewById(R.id.btn_add_step_detail);
        RecyclerView recyclerViewStepsDetail = dialogView.findViewById(R.id.recycler_view_steps_detail);
        TextView tvNoSteps = dialogView.findViewById(R.id.tv_no_steps);

        // Set task information
        tvTaskTitle.setText(task.getTitle());

        if (task.getDescription() != null && !task.getDescription().trim().isEmpty()) {
            tvTaskDescription.setText(task.getDescription());
            tvTaskDescription.setVisibility(View.VISIBLE);
        }

        chipCategory.setText(task.getCategory() != null ? task.getCategory().getName() : "Chung");
        chipPriority.setText(task.getPriority().toString());

        // Setup steps - Load from database
        List<TaskStep> steps = new ArrayList<>();

        // Load steps from database if task has ID
        if (task.getId() != null) {
            try {
                int taskId = Integer.parseInt(task.getId());
                todoViewModel.getStepsByTaskId(taskId).observe(getViewLifecycleOwner(), stepEntities -> {
                    steps.clear();
                    if (stepEntities != null) {
                        for (TaskStepEntity entity : stepEntities) {
                            TaskStep step = new TaskStep();
                            step.setId(String.valueOf(entity.getId()));
                            step.setTitle(entity.getTitle());
                            step.setDescription(entity.getDescription());
                            step.setCompleted(entity.isCompleted());
                            step.setOrder(entity.getStepOrder());
                            step.setCreatedAt(entity.getCreatedAt());
                            steps.add(step);
                        }
                    }

                    // Update UI
                    if (!steps.isEmpty()) {
                        layoutProgress.setVisibility(View.VISIBLE);
                        tvNoSteps.setVisibility(View.GONE);

                        // Calculate progress
                        int completedSteps = 0;
                        for (TaskStep step : steps) {
                            if (step.isCompleted()) {
                                completedSteps++;
                            }
                        }
                        int totalSteps = steps.size();
                        float progress = totalSteps > 0 ? (float) completedSteps / totalSteps * 100f : 0f;

                        progressBar.setMax(100);
                        progressBar.setProgress((int) progress);
                        tvProgressText.setText(completedSteps + "/" + totalSteps + " bước hoàn thành");
                    } else {
                        layoutProgress.setVisibility(View.GONE);
                        tvNoSteps.setVisibility(View.VISIBLE);
                    }

                    // Update adapter
                    TaskStepDetailAdapter currentAdapter = (TaskStepDetailAdapter) recyclerViewStepsDetail.getAdapter();
                    if (currentAdapter != null) {
                        currentAdapter.updateSteps(steps);
                    }
                });
            } catch (NumberFormatException e) {
                // Handle invalid task ID
                layoutProgress.setVisibility(View.GONE);
                tvNoSteps.setVisibility(View.VISIBLE);
            }
        } else {
            layoutProgress.setVisibility(View.GONE);
            tvNoSteps.setVisibility(View.VISIBLE);
        }

        // Setup steps RecyclerView
        TaskStepDetailAdapter stepAdapter = new TaskStepDetailAdapter(steps, new TaskStepDetailAdapter.OnStepActionListener() {
            @Override
            public void onStepCompletionChanged(int position, TaskStep step, boolean isCompleted) {
                // Log để debug sự kiện step completion changed
                android.util.Log.d("TaskListFragment", "onStepCompletionChanged: position=" + position +
                    ", stepTitle=" + step.getTitle() + ", isCompleted=" + isCompleted);

                // Tính toán lại số bước đã hoàn thành trong task
                int completedSteps = task.getCompletedStepsCount();
                // Lấy tổng số bước trong task
                int totalSteps = task.getTotalStepsCount();
                // Tính phần trăm tiến độ hoàn thành
                float progress = task.getProgressPercentage();

                // Log thông tin tiến độ
                android.util.Log.d("TaskListFragment", "Progress: " + completedSteps + "/" + totalSteps +
                    " = " + progress + "%");

                // Cập nhật thanh tiến độ trên giao diện
                progressBar.setProgress((int) progress);
                // Cập nhật text hiển thị tiến độ (VD: "2/5 bước hoàn thành")
                tvProgressText.setText(completedSteps + "/" + totalSteps + " bước hoàn thành");

                // Cập nhật step vào database (chỉ khi step có ID hợp lệ)
                if (step.getId() != null) {
                    // Log thông tin step sẽ được cập nhật
                    android.util.Log.d("TaskListFragment", "Updating step in database: stepId=" + step.getId() +
                        ", taskId=" + task.getId() + ", completed=" + step.isCompleted());

                    // Chạy trên background thread để không block UI
                    TodoDatabase.databaseWriteExecutor.execute(() -> {
                        try {
                            // Chuyển đổi ID từ String sang int
                            int stepId = Integer.parseInt(step.getId());
                            // Tạo entity để lưu vào database
                            TaskStepEntity stepEntity = new TaskStepEntity();
                            stepEntity.setId(stepId);
                            stepEntity.setTaskId(Integer.parseInt(task.getId()));
                            stepEntity.setTitle(step.getTitle());
                            stepEntity.setDescription(step.getDescription());
                            // Cập nhật trạng thái completed mới
                            stepEntity.setCompleted(step.isCompleted());
                            stepEntity.setStepOrder(step.getOrder());
                            stepEntity.setCreatedAt(step.getCreatedAt());

                            // Log trước khi cập nhật database
                            android.util.Log.d("TaskListFragment", "About to update step in DB: " + stepEntity.getId());

                            // Gọi repository để cập nhật step trong database
                            todoViewModel.getRepository().updateTaskStepSync(stepEntity);

                            // Log sau khi cập nhật thành công
                            android.util.Log.d("TaskListFragment", "Step updated successfully in DB");

                            // Kiểm tra xem tất cả các bước đã hoàn thành chưa để cập nhật trạng thái task
                            boolean allStepsCompleted = true;
                            // Đếm số step đã hoàn thành để log
                            int completedCount = 0;

                            // Duyệt qua tất cả các step để kiểm tra
                            for (TaskStep s : steps) {
                                // Log từng step để debug
                                android.util.Log.d("TaskListFragment", "Checking step: " + s.getTitle() +
                                    ", completed: " + s.isCompleted());

                                // Đếm số step đã hoàn thành
                                if (s.isCompleted()) {
                                    completedCount++;
                                }

                                // Nếu có bất kỳ step nào chưa hoàn thành
                                if (!s.isCompleted()) {
                                    allStepsCompleted = false;
                                    break; // Thoát khỏi vòng lặp ngay lập tức
                                }
                            }

                            // Log kết quả kiểm tra
                            android.util.Log.d("TaskListFragment", "Step completion check: " + completedCount +
                                "/" + steps.size() + " completed, allStepsCompleted=" + allStepsCompleted);

                            // Cập nhật trạng thái hoàn thành của task dựa trên kết quả kiểm tra
                            TaskEntity taskEntity = new TaskEntity();
                            taskEntity.setId(Integer.parseInt(task.getId()));
                            taskEntity.setTitle(task.getTitle());
                            taskEntity.setDescription(task.getDescription());
                            taskEntity.setCategoryId(Integer.parseInt(task.getCategory().getId()));
                            taskEntity.setPriority(task.getPriority());

                            // Logic cập nhật trạng thái completed của task:
                            // Task được đánh dấu hoàn thành KHI VÀ CHỈ KHI: tất cả steps hoàn thành VÀ có ít nhất 1 step
                            boolean oldTaskCompleted = task.isCompleted(); // Trạng thái cũ của task
                            boolean newTaskCompleted = allStepsCompleted && !steps.isEmpty(); // Trạng thái mới dựa trên steps

                            // Log chi tiết logic kiểm tra
                            android.util.Log.d("TaskListFragment", "Task completion logic check:");
                            android.util.Log.d("TaskListFragment", "  - Old task completed: " + oldTaskCompleted);
                            android.util.Log.d("TaskListFragment", "  - All steps completed: " + allStepsCompleted);
                            android.util.Log.d("TaskListFragment", "  - Steps not empty: " + !steps.isEmpty());
                            android.util.Log.d("TaskListFragment", "  - New task completed: " + newTaskCompleted);

                            // Áp dụng trạng thái mới cho task (dựa trên logic kiểm tra steps)
                            taskEntity.setCompleted(newTaskCompleted);

                            // Log kết quả cập nhật
                            if (oldTaskCompleted != newTaskCompleted) {
                                android.util.Log.i("TaskListFragment", "Task completion status CHANGED: " +
                                    oldTaskCompleted + " → " + newTaskCompleted + " for task: " + task.getTitle());
                            } else {
                                android.util.Log.d("TaskListFragment", "Task completion status UNCHANGED: " +
                                    newTaskCompleted + " for task: " + task.getTitle());
                            }
                            taskEntity.setCreatedAt(task.getCreatedAt());
                            taskEntity.setDueDate(task.getDueDate()); // Thêm due date
                            taskEntity.setUserId(authManager.getCurrentUserId()); // QUAN TRỌNG: Thêm userId để task không bị mất

                            // Log chi tiết thông tin task entity sẽ được cập nhật trong database
                            android.util.Log.d("TaskListFragment", "About to update task entity in database:");
                            android.util.Log.d("TaskListFragment", "  - Task ID: " + taskEntity.getId());
                            android.util.Log.d("TaskListFragment", "  - Task Title: " + taskEntity.getTitle());
                            android.util.Log.d("TaskListFragment", "  - Task Completed: " + taskEntity.isCompleted());
                            android.util.Log.d("TaskListFragment", "  - User ID: " + taskEntity.getUserId());
                            android.util.Log.d("TaskListFragment", "  - Category ID: " + taskEntity.getCategoryId());
                            android.util.Log.d("TaskListFragment", "  - Due Date: " + taskEntity.getDueDate());

                            // Gọi repository để cập nhật task trong database
                            todoViewModel.getRepository().updateTaskSync(taskEntity);

                            // Log sau khi cập nhật task thành công
                            android.util.Log.i("TaskListFragment", "Task updated successfully in database - " +
                                "Task: " + taskEntity.getTitle() + " (completed: " + taskEntity.isCompleted() + ")");
                        } catch (NumberFormatException e) {
                            // Handle invalid ID
                        }
                    });
                }
            }

            @Override
            public void onStepEditClicked(int position, TaskStep step) {
                // Log sự kiện edit step được click
                android.util.Log.d("TaskListFragment", "onStepEditClicked: position=" + position +
                    ", stepId=" + step.getId() + ", stepTitle=" + step.getTitle());

                // Tạo dialog để chỉnh sửa step
                AlertDialog.Builder builder = new AlertDialog.Builder(getContext());

                // Inflate layout mới cho dialog edit step
                View editStepView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_edit_task_step, null);

                // Lấy các view components từ layout mới
                EditText etStepTitle = editStepView.findViewById(R.id.etStepTitle);
                EditText etStepDescription = editStepView.findViewById(R.id.etStepDescription);

                // Điền thông tin hiện tại của step vào các EditText
                etStepTitle.setText(step.getTitle());
                etStepDescription.setText(step.getDescription());

                // Log thông tin step hiện tại
                android.util.Log.d("TaskListFragment", "Current step info - Title: " + step.getTitle() +
                    ", Description: " + step.getDescription() + ", Completed: " + step.isCompleted());

                // Tạo và hiển thị dialog
                AlertDialog editDialog = builder.setView(editStepView)
                    .setTitle("Chỉnh sửa bước")
                    .setPositiveButton("Cập nhật", (dialog, which) -> {
                        // Lấy text mới từ EditText
                        String newTitle = etStepTitle.getText().toString().trim();
                        String newDescription = etStepDescription.getText().toString().trim();

                        // Log thông tin mới
                        android.util.Log.d("TaskListFragment", "New step info - Title: " + newTitle +
                            ", Description: " + newDescription);

                        // Kiểm tra title không được rỗng
                        if (newTitle.isEmpty()) {
                            Toast.makeText(getContext(), "Tiêu đề bước không được để trống", Toast.LENGTH_SHORT).show();
                            android.util.Log.w("TaskListFragment", "Edit step failed: empty title");
                            return;
                        }

                        // Cập nhật thông tin step trong model
                        step.setTitle(newTitle);
                        step.setDescription(newDescription);

                        // Log thông tin step đã được cập nhật trong model
                        android.util.Log.d("TaskListFragment", "Step model updated - Title: " + step.getTitle() +
                            ", Description: " + step.getDescription());

                        // Giao diện sẽ được cập nhật tự động khi database thay đổi thông qua LiveData observer
                        // Không cần gọi notifyItemChanged() ở đây vì có thể gây conflict với scope của adapter

                        // Log trước khi cập nhật database
                        android.util.Log.d("TaskListFragment", "About to update step in database: stepId=" +
                            step.getId() + ", taskId=" + task.getId());

                        // Cập nhật step trong database trên background thread
                        if (step.getId() != null) {
                            TodoDatabase.databaseWriteExecutor.execute(() -> {
                                try {
                                    // Chuyển đổi ID từ String sang int
                                    int stepId = Integer.parseInt(step.getId());

                                    // Tạo TaskStepEntity với đầy đủ thông tin
                                    TaskStepEntity stepEntity = new TaskStepEntity();
                                    stepEntity.setId(stepId);
                                    stepEntity.setTaskId(Integer.parseInt(task.getId()));
                                    stepEntity.setTitle(newTitle); // Tiêu đề mới
                                    stepEntity.setDescription(newDescription); // Mô tả mới
                                    stepEntity.setCompleted(step.isCompleted()); // Giữ nguyên trạng thái completed
                                    stepEntity.setStepOrder(step.getOrder()); // Giữ nguyên thứ tự
                                    stepEntity.setCreatedAt(step.getCreatedAt()); // Giữ nguyên thời gian tạo

                                    // Log thông tin entity sẽ được cập nhật
                                    android.util.Log.d("TaskListFragment", "Updating step entity: " +
                                        "id=" + stepEntity.getId() + ", title=" + stepEntity.getTitle() +
                                        ", description=" + stepEntity.getDescription());

                                    // Gọi repository để cập nhật step trong database
                                    todoViewModel.getRepository().updateTaskStepSync(stepEntity);

                                    // Log thành công
                                    android.util.Log.d("TaskListFragment", "Step updated successfully in database");

                                    // Hiển thị thông báo thành công trên UI thread
                                    if (getActivity() != null) {
                                        getActivity().runOnUiThread(() -> {
                                            Toast.makeText(getContext(), "Đã cập nhật bước: " + newTitle,
                                                Toast.LENGTH_SHORT).show();
                                        });
                                    }

                                } catch (NumberFormatException e) {
                                    // Log lỗi khi parse ID
                                    android.util.Log.e("TaskListFragment", "Error parsing step ID: " + step.getId(), e);
                                    if (getActivity() != null) {
                                        getActivity().runOnUiThread(() -> {
                                            Toast.makeText(getContext(), "Lỗi: ID bước không hợp lệ",
                                                Toast.LENGTH_SHORT).show();
                                        });
                                    }
                                } catch (Exception e) {
                                    // Log lỗi chung khi cập nhật database
                                    android.util.Log.e("TaskListFragment", "Error updating step in database", e);
                                    if (getActivity() != null) {
                                        getActivity().runOnUiThread(() -> {
                                            Toast.makeText(getContext(), "Lỗi khi cập nhật bước",
                                                Toast.LENGTH_SHORT).show();
                                        });
                                    }
                                }
                            });
                        } else {
                            // Log cảnh báo khi step không có ID
                            android.util.Log.w("TaskListFragment", "Cannot update step: step ID is null");
                            Toast.makeText(getContext(), "Lỗi: Không thể cập nhật bước", Toast.LENGTH_SHORT).show();
                        }
                    })
                    .setNegativeButton("Hủy", (dialog, which) -> {
                        // Log khi user hủy chỉnh sửa
                        android.util.Log.d("TaskListFragment", "Edit step cancelled by user");
                        dialog.dismiss();
                    })
                    .create();

                // Log khi hiển thị dialog
                android.util.Log.d("TaskListFragment", "Showing edit step dialog");
                editDialog.show();
            }

            @Override
            public void onStepDeleteClicked(int position, TaskStep step) {
                // Delete step from database
                if (step.getId() != null) {
                    TodoDatabase.databaseWriteExecutor.execute(() -> {
                        try {
                            int stepId = Integer.parseInt(step.getId());
                            TaskStepEntity stepEntity = new TaskStepEntity();
                            stepEntity.setId(stepId);
                            todoViewModel.getRepository().deleteTaskStepSync(stepEntity);
                        } catch (NumberFormatException e) {
                            // Handle invalid ID
                        }
                    });
                }
                steps.remove(position);
                updateStepsAdapter();

                // Update progress
                if (steps.isEmpty()) {
                    layoutProgress.setVisibility(View.GONE);
                    tvNoSteps.setVisibility(View.VISIBLE);
                } else {
                    int completedSteps = task.getCompletedStepsCount();
                    int totalSteps = task.getTotalStepsCount();
                    float progress = task.getProgressPercentage();

                    progressBar.setProgress((int) progress);
                    tvProgressText.setText(completedSteps + "/" + totalSteps + " bước hoàn thành");
                }

                Toast.makeText(getContext(), "Đã xóa bước", Toast.LENGTH_SHORT).show();
            }

            private void updateStepsAdapter() {
                TaskStepDetailAdapter currentAdapter = (TaskStepDetailAdapter) recyclerViewStepsDetail.getAdapter();
                if (currentAdapter != null) {
                    currentAdapter.updateSteps(steps);
                }
            }
        });

        recyclerViewStepsDetail.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerViewStepsDetail.setAdapter(stepAdapter);

        // Add step button click
        btnAddStepDetail.setOnClickListener(v -> {
            showAddStepDialog(task, steps, () -> {
                // Update adapter after adding step
                TaskStepDetailAdapter currentAdapter = (TaskStepDetailAdapter) recyclerViewStepsDetail.getAdapter();
                if (currentAdapter != null) {
                    currentAdapter.updateSteps(steps);
                }

                // Update progress
                int completedSteps = 0;
                for (TaskStep step : steps) {
                    if (step.isCompleted()) {
                        completedSteps++;
                    }
                }
                int totalSteps = steps.size();
                float progress = totalSteps > 0 ? (float) completedSteps / totalSteps * 100f : 0f;

                progressBar.setMax(100);
                progressBar.setProgress((int) progress);
                tvProgressText.setText(completedSteps + "/" + totalSteps + " bước hoàn thành");

                if (!steps.isEmpty()) {
                    layoutProgress.setVisibility(View.VISIBLE);
                    tvNoSteps.setVisibility(View.GONE);
                } else {
                    layoutProgress.setVisibility(View.GONE);
                    tvNoSteps.setVisibility(View.VISIBLE);
                }
            });
        });

        // Create and show dialog
        AlertDialog dialog = builder.setView(dialogView)
                .setTitle("Chi tiết nhiệm vụ")
                .setPositiveButton("Đóng", (dialogInterface, which) -> {
                    // Log khi user ấn nút đóng
                    android.util.Log.d("TaskListFragment", "Dialog closed by user - task: " + task.getTitle());
                    // Đóng dialog (hành vi mặc định)
                    dialogInterface.dismiss();
                })
                .create();

        // Log khi dialog được hiển thị
        android.util.Log.d("TaskListFragment", "Showing task detail dialog for: " + task.getTitle() +
            " (ID: " + task.getId() + ")");
        dialog.show();
    }

    private void showAddStepDialog(Task task, List<TaskStep> steps, Runnable onStepAdded) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());

        // Create simple input dialog
        EditText etStepTitle = new EditText(getContext());
        etStepTitle.setHint("Nhập tên bước...");
        etStepTitle.setPadding(50, 30, 50, 30);

        builder.setTitle("Thêm bước mới")
                .setView(etStepTitle)
                .setPositiveButton("Thêm", (dialog, which) -> {
                    String stepTitle = etStepTitle.getText().toString().trim();
                    if (!stepTitle.isEmpty()) {
                        // Create new step
                        TaskStep newStep = new TaskStep();
                        newStep.setTitle(stepTitle);
                        newStep.setDescription("");
                        newStep.setCompleted(false);
                        newStep.setOrder(steps.size());
                        newStep.setCreatedAt(new Date());

                        // Add to database
                        if (task.getId() != null) {
                            TodoDatabase.databaseWriteExecutor.execute(() -> {
                                try {
                                    int taskId = Integer.parseInt(task.getId());
                                    TaskStepEntity stepEntity = new TaskStepEntity();
                                    stepEntity.setTaskId(taskId);
                                    stepEntity.setTitle(stepTitle);
                                    stepEntity.setDescription("");
                                    stepEntity.setCompleted(false);
                                    stepEntity.setStepOrder(steps.size());
                                    stepEntity.setCreatedAt(new Date());

                                    long stepId = todoViewModel.getRepository().insertTaskStepSync(stepEntity);
                                    newStep.setId(String.valueOf(stepId));

                                    // Update UI on main thread
                                    if (getActivity() != null) {
                                        getActivity().runOnUiThread(() -> {
                                            steps.add(newStep);
                                            if (onStepAdded != null) {
                                                onStepAdded.run();
                                            }
                                        });
                                    }
                                } catch (NumberFormatException e) {
                                    // Handle invalid task ID
                                }
                            });
                        }
                    }
                })
                .setNegativeButton("Hủy", null)
                .show();
    }

    private void showAddCategoryDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_add_category, null);

        EditText etCategoryName = dialogView.findViewById(R.id.et_category_name);

        // Icon selection
        String[] selectedIcon = {"📝"}; // Default icon
        String[] selectedColor = {"#2196F3"}; // Default color

        // Setup icon buttons
        int[] iconButtonIds = {
            R.id.btn_icon_work, R.id.btn_icon_personal, R.id.btn_icon_study,
            R.id.btn_icon_health, R.id.btn_icon_shopping, R.id.btn_icon_family,
            R.id.btn_icon_travel, R.id.btn_icon_food, R.id.btn_icon_hobby,
            R.id.btn_icon_finance, R.id.btn_icon_home, R.id.btn_icon_other
        };

        String[] icons = {"💼", "👤", "📚", "💪", "🛒", "👨‍👩‍👧‍👦", "✈", "🍽️", "🎨", "💰", "🏠", "📝"};

        for (int i = 0; i < iconButtonIds.length; i++) {
            Button iconButton = dialogView.findViewById(iconButtonIds[i]);
            final String icon = icons[i];
            iconButton.setOnClickListener(v -> {
                selectedIcon[0] = icon;
                // Reset all button backgrounds
                for (int id : iconButtonIds) {
                    dialogView.findViewById(id).setBackgroundTintList(null);
                }
                // Highlight selected button
                iconButton.setBackgroundTintList(getResources().getColorStateList(R.color.primary, null));
            });
        }

        // Setup color selection
        int[] colorViewIds = {
            R.id.color_blue, R.id.color_green, R.id.color_orange,
            R.id.color_red, R.id.color_purple, R.id.color_pink
        };

        String[] colors = {"#2196F3", "#4CAF50", "#FF9800", "#F44336", "#9C27B0", "#E91E63"};

        for (int i = 0; i < colorViewIds.length; i++) {
            View colorView = dialogView.findViewById(colorViewIds[i]);
            final String color = colors[i];
            colorView.setOnClickListener(v -> {
                selectedColor[0] = color;
            });
        }

        builder.setView(dialogView)
                .setTitle("Thêm danh mục mới")
                .setPositiveButton("Thêm", (dialog, which) -> {
                    String categoryName = etCategoryName.getText().toString().trim();
                    if (!categoryName.isEmpty()) {
                        addCategoryToDatabase(categoryName, selectedIcon[0], selectedColor[0]);
                    } else {
                        Toast.makeText(getContext(), "Vui lòng nhập tên danh mục", Toast.LENGTH_SHORT).show();
                    }
                })
                .setNegativeButton("Hủy", null)
                .show();
    }

    private void addCategoryToDatabase(String name, String icon, String color) {
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setName(name);
        categoryEntity.setIcon(icon);
        categoryEntity.setColor(color);
        categoryEntity.setUserId(authManager.getCurrentUserId()); // Set user ID for custom categories

        TodoDatabase.databaseWriteExecutor.execute(() -> {
            todoViewModel.getRepository().insertCategorySync(categoryEntity);

            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Đã thêm danh mục: " + name, Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
}

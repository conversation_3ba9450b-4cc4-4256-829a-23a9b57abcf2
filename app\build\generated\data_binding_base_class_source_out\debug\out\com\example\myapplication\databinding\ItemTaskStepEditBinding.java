// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskStepEditBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton btnDeleteStep;

  @NonNull
  public final TextInputEditText etStepDescription;

  @NonNull
  public final TextInputEditText etStepTitle;

  @NonNull
  public final ImageView ivDragHandle;

  private ItemTaskStepEditBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton btnDeleteStep, @NonNull TextInputEditText etStepDescription,
      @NonNull TextInputEditText etStepTitle, @NonNull ImageView ivDragHandle) {
    this.rootView = rootView;
    this.btnDeleteStep = btnDeleteStep;
    this.etStepDescription = etStepDescription;
    this.etStepTitle = etStepTitle;
    this.ivDragHandle = ivDragHandle;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskStepEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskStepEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task_step_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskStepEditBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_delete_step;
      ImageButton btnDeleteStep = ViewBindings.findChildViewById(rootView, id);
      if (btnDeleteStep == null) {
        break missingId;
      }

      id = R.id.et_step_description;
      TextInputEditText etStepDescription = ViewBindings.findChildViewById(rootView, id);
      if (etStepDescription == null) {
        break missingId;
      }

      id = R.id.et_step_title;
      TextInputEditText etStepTitle = ViewBindings.findChildViewById(rootView, id);
      if (etStepTitle == null) {
        break missingId;
      }

      id = R.id.iv_drag_handle;
      ImageView ivDragHandle = ViewBindings.findChildViewById(rootView, id);
      if (ivDragHandle == null) {
        break missingId;
      }

      return new ItemTaskStepEditBinding((MaterialCardView) rootView, btnDeleteStep,
          etStepDescription, etStepTitle, ivDragHandle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

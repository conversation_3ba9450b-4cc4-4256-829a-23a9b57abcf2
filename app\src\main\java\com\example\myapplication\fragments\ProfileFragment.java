package com.example.myapplication.fragments;

import android.app.AlertDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.myapplication.R;
import com.example.myapplication.activities.AuthActivity;
import com.example.myapplication.auth.AuthManager;
import com.example.myapplication.models.User;

import android.content.Intent;

public class ProfileFragment extends Fragment {

    private LinearLayout layoutLoggedIn;
    private LinearLayout layoutNotLoggedIn;
    private Button btnLogin;
    private Button btnLogout;
    private TextView tvUserName;
    private TextView tvUserEmail;
    private ImageView ivUserAvatar;
    private TextView tvUserInitials;

    private User currentUser;
    private AuthManager authManager;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_profile, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        authManager = AuthManager.getInstance(requireContext());
        initViews(view);
        setupClickListeners();
        loadUserData();
        updateUI();
    }

    private void initViews(View view) {
        layoutLoggedIn = view.findViewById(R.id.layout_logged_in);
        layoutNotLoggedIn = view.findViewById(R.id.layout_not_logged_in);
        btnLogin = view.findViewById(R.id.btn_login);
        btnLogout = view.findViewById(R.id.btn_logout);
        tvUserName = view.findViewById(R.id.tv_user_name);
        tvUserEmail = view.findViewById(R.id.tv_user_email);
        ivUserAvatar = view.findViewById(R.id.iv_user_avatar);
        tvUserInitials = view.findViewById(R.id.tv_user_initials);
    }

    private void setupClickListeners() {
        btnLogin.setOnClickListener(v -> navigateToAuth());
        btnLogout.setOnClickListener(v -> logout());
        
        // Profile menu items
        View rootView = getView();
        if (rootView != null) {
            rootView.findViewById(R.id.menu_statistics).setOnClickListener(v ->
                Toast.makeText(getContext(), "Thống kê", Toast.LENGTH_SHORT).show());

            rootView.findViewById(R.id.menu_settings).setOnClickListener(v ->
                Toast.makeText(getContext(), "Cài đặt", Toast.LENGTH_SHORT).show());

            rootView.findViewById(R.id.menu_help).setOnClickListener(v ->
                Toast.makeText(getContext(), "Trợ giúp", Toast.LENGTH_SHORT).show());

            rootView.findViewById(R.id.menu_about).setOnClickListener(v ->
                Toast.makeText(getContext(), "Về ứng dụng", Toast.LENGTH_SHORT).show());
        }
    }

    private void loadUserData() {
        currentUser = authManager.getCurrentUser();
    }

    private void navigateToAuth() {
        Intent intent = new Intent(getActivity(), AuthActivity.class);
        startActivity(intent);
        if (getActivity() != null) {
            getActivity().finish();
        }
    }



    private void logout() {
        new AlertDialog.Builder(getContext())
                .setTitle("Đăng xuất")
                .setMessage("Bạn có chắc chắn muốn đăng xuất?")
                .setPositiveButton("Đăng xuất", (dialog, which) -> {
                    authManager.logout();
                    navigateToAuth();
                    Toast.makeText(getContext(), "Đã đăng xuất", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton("Hủy", null)
                .show();
    }

    private void updateUI() {
        if (authManager.isLoggedIn() && currentUser != null) {
            layoutLoggedIn.setVisibility(View.VISIBLE);
            layoutNotLoggedIn.setVisibility(View.GONE);

            tvUserName.setText(currentUser.getDisplayName());
            tvUserEmail.setText(currentUser.getEmail());
            tvUserInitials.setText(currentUser.getInitials());
        } else {
            layoutLoggedIn.setVisibility(View.GONE);
            layoutNotLoggedIn.setVisibility(View.VISIBLE);
        }
    }
}

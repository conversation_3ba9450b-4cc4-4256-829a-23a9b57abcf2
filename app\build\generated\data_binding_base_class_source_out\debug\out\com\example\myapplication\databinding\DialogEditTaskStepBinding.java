// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogEditTaskStepBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText etStepDescription;

  @NonNull
  public final TextInputEditText etStepTitle;

  private DialogEditTaskStepBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText etStepDescription, @NonNull TextInputEditText etStepTitle) {
    this.rootView = rootView;
    this.etStepDescription = etStepDescription;
    this.etStepTitle = etStepTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogEditTaskStepBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogEditTaskStepBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_edit_task_step, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogEditTaskStepBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.etStepDescription;
      TextInputEditText etStepDescription = ViewBindings.findChildViewById(rootView, id);
      if (etStepDescription == null) {
        break missingId;
      }

      id = R.id.etStepTitle;
      TextInputEditText etStepTitle = ViewBindings.findChildViewById(rootView, id);
      if (etStepTitle == null) {
        break missingId;
      }

      return new DialogEditTaskStepBinding((LinearLayout) rootView, etStepDescription, etStepTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

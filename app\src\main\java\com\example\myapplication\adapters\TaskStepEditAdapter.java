package com.example.myapplication.adapters;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapplication.R;
import com.example.myapplication.models.TaskStep;
import com.google.android.material.textfield.TextInputEditText;

import java.util.List;

public class TaskStepEditAdapter extends RecyclerView.Adapter<TaskStepEditAdapter.StepViewHolder> {

    private List<TaskStep> stepList;
    private OnStepActionListener listener;

    public interface OnStepActionListener {
        void onStepDeleted(int position);
        void onStepChanged(int position, TaskStep step);
    }

    public TaskStepEditAdapter(List<TaskStep> stepList, OnStepActionListener listener) {
        this.stepList = stepList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public StepViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_task_step_edit, parent, false);
        return new StepViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull StepViewHolder holder, int position) {
        TaskStep step = stepList.get(position);
        holder.bind(step, position);
    }

    @Override
    public int getItemCount() {
        return stepList.size();
    }

    public void updateSteps(List<TaskStep> newSteps) {
        this.stepList = newSteps;
        notifyDataSetChanged();
    }

    public class StepViewHolder extends RecyclerView.ViewHolder {
        private TextInputEditText etStepTitle;
        private TextInputEditText etStepDescription;
        private ImageButton btnDeleteStep;

        public StepViewHolder(@NonNull View itemView) {
            super(itemView);
            etStepTitle = itemView.findViewById(R.id.et_step_title);
            etStepDescription = itemView.findViewById(R.id.et_step_description);
            btnDeleteStep = itemView.findViewById(R.id.btn_delete_step);
        }

        public void bind(TaskStep step, int position) {
            // Clear previous text watchers to avoid conflicts
            etStepTitle.setTag(null);
            etStepDescription.setTag(null);

            // Set text
            etStepTitle.setText(step.getTitle());
            etStepDescription.setText(step.getDescription());

            // Add text watchers
            TextWatcher titleWatcher = new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(Editable s) {
                    step.setTitle(s.toString());
                    if (listener != null) {
                        listener.onStepChanged(position, step);
                    }
                }
            };

            TextWatcher descriptionWatcher = new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(Editable s) {
                    step.setDescription(s.toString());
                    if (listener != null) {
                        listener.onStepChanged(position, step);
                    }
                }
            };

            etStepTitle.addTextChangedListener(titleWatcher);
            etStepDescription.addTextChangedListener(descriptionWatcher);

            // Store watchers in tags for cleanup
            etStepTitle.setTag(titleWatcher);
            etStepDescription.setTag(descriptionWatcher);

            // Delete button click
            btnDeleteStep.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onStepDeleted(position);
                }
            });
        }
    }
}

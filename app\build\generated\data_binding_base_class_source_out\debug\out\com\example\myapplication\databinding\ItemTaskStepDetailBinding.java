// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskStepDetailBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton btnDeleteStepDetail;

  @NonNull
  public final ImageButton btnEditStep;

  @NonNull
  public final CheckBox cbStepCompleted;

  @NonNull
  public final TextView tvStepDescription;

  @NonNull
  public final TextView tvStepStatus;

  @NonNull
  public final TextView tvStepTitle;

  private ItemTaskStepDetailBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton btnDeleteStepDetail, @NonNull ImageButton btnEditStep,
      @NonNull CheckBox cbStepCompleted, @NonNull TextView tvStepDescription,
      @NonNull TextView tvStepStatus, @NonNull TextView tvStepTitle) {
    this.rootView = rootView;
    this.btnDeleteStepDetail = btnDeleteStepDetail;
    this.btnEditStep = btnEditStep;
    this.cbStepCompleted = cbStepCompleted;
    this.tvStepDescription = tvStepDescription;
    this.tvStepStatus = tvStepStatus;
    this.tvStepTitle = tvStepTitle;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskStepDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskStepDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task_step_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskStepDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_delete_step_detail;
      ImageButton btnDeleteStepDetail = ViewBindings.findChildViewById(rootView, id);
      if (btnDeleteStepDetail == null) {
        break missingId;
      }

      id = R.id.btn_edit_step;
      ImageButton btnEditStep = ViewBindings.findChildViewById(rootView, id);
      if (btnEditStep == null) {
        break missingId;
      }

      id = R.id.cb_step_completed;
      CheckBox cbStepCompleted = ViewBindings.findChildViewById(rootView, id);
      if (cbStepCompleted == null) {
        break missingId;
      }

      id = R.id.tv_step_description;
      TextView tvStepDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvStepDescription == null) {
        break missingId;
      }

      id = R.id.tv_step_status;
      TextView tvStepStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStepStatus == null) {
        break missingId;
      }

      id = R.id.tv_step_title;
      TextView tvStepTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvStepTitle == null) {
        break missingId;
      }

      return new ItemTaskStepDetailBinding((MaterialCardView) rootView, btnDeleteStepDetail,
          btnEditStep, cbStepCompleted, tvStepDescription, tvStepStatus, tvStepTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

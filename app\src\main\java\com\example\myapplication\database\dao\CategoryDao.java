package com.example.myapplication.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.example.myapplication.database.entities.CategoryEntity;

import java.util.List;

@Dao
public interface CategoryDao {

    // Insert operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertCategory(CategoryEntity category);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertCategories(List<CategoryEntity> categories);

    // Update operations
    @Update
    void updateCategory(CategoryEntity category);

    @Query("UPDATE categories SET task_count = :taskCount WHERE id = :categoryId")
    void updateCategoryTaskCount(int categoryId, int taskCount);

    // Delete operations
    @Delete
    void deleteCategory(CategoryEntity category);

    @Query("DELETE FROM categories WHERE id = :categoryId")
    void deleteCategoryById(int categoryId);

    // Select operations
    @Query("SELECT * FROM categories ORDER BY name ASC")
    LiveData<List<CategoryEntity>> getAllCategories();

    @Query("SELECT * FROM categories WHERE user_id IS NULL OR user_id = :userId ORDER BY name ASC")
    LiveData<List<CategoryEntity>> getCategoriesForUser(int userId);

    @Query("SELECT COUNT(*) FROM categories")
    int getCategoryCount();

    @Query("SELECT * FROM categories WHERE id = :categoryId")
    LiveData<CategoryEntity> getCategoryById(int categoryId);

    @Query("SELECT * FROM categories WHERE name = :name LIMIT 1")
    CategoryEntity getCategoryByName(String name);



    // Get categories with task counts
    @Query("SELECT c.*, COUNT(t.id) as task_count FROM categories c LEFT JOIN tasks t ON c.id = t.category_id GROUP BY c.id ORDER BY c.name ASC")
    LiveData<List<CategoryEntity>> getCategoriesWithTaskCount();
}

package com.example.myapplication.auth;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.example.myapplication.database.TodoDatabase;
import com.example.myapplication.database.entities.UserEntity;
import com.example.myapplication.models.User;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class AuthManager {
    private static final String TAG = "AuthManager";
    private static final String PREFS_NAME = "auth_prefs";
    private static final String KEY_USER_ID = "user_id";
    private static final String KEY_USERNAME = "username";
    private static final String KEY_IS_LOGGED_IN = "is_logged_in";

    private static AuthManager instance;
    private Context context;
    private SharedPreferences prefs;
    private TodoDatabase database;
    private ExecutorService executor;
    private User currentUser;

    private AuthManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.database = TodoDatabase.getDatabase(context);
        this.executor = Executors.newSingleThreadExecutor();
        loadCurrentUser();
    }

    public static synchronized AuthManager getInstance(Context context) {
        if (instance == null) {
            instance = new AuthManager(context);
        }
        return instance;
    }

    public interface AuthCallback {
        void onSuccess(User user);
        void onError(String error);
    }

    public void register(String username, String email, String fullName, String password, AuthCallback callback) {
        executor.execute(() -> {
            try {
                // Check if username or email already exists
                if (database.userDao().checkUsernameExists(username) > 0) {
                    callback.onError("Tên đăng nhập đã tồn tại");
                    return;
                }

                if (database.userDao().checkEmailExists(email) > 0) {
                    callback.onError("Email đã được sử dụng");
                    return;
                }

                // Hash password
                String passwordHash = hashPassword(password);
                if (passwordHash == null) {
                    callback.onError("Lỗi mã hóa mật khẩu");
                    return;
                }

                // Create user entity
                UserEntity userEntity = new UserEntity(username, email, fullName, passwordHash);
                long userId = database.userDao().insertUser(userEntity);

                if (userId > 0) {
                    userEntity.setId((int) userId);
                    User user = userEntity.toUser();
                    setCurrentUser(user);
                    callback.onSuccess(user);
                } else {
                    callback.onError("Không thể tạo tài khoản");
                }
            } catch (Exception e) {
                Log.e(TAG, "Registration error", e);
                callback.onError("Lỗi đăng ký: " + e.getMessage());
            }
        });
    }

    public void login(String username, String password, AuthCallback callback) {
        executor.execute(() -> {
            try {
                String passwordHash = hashPassword(password);
                if (passwordHash == null) {
                    callback.onError("Lỗi mã hóa mật khẩu");
                    return;
                }

                UserEntity userEntity = database.userDao().authenticateUser(username, passwordHash);
                if (userEntity != null && userEntity.isActive()) {
                    // Update last login time
                    database.userDao().updateLastLogin(userEntity.getId(), new Date());
                    
                    User user = userEntity.toUser();
                    setCurrentUser(user);
                    callback.onSuccess(user);
                } else {
                    callback.onError("Tên đăng nhập hoặc mật khẩu không đúng");
                }
            } catch (Exception e) {
                Log.e(TAG, "Login error", e);
                callback.onError("Lỗi đăng nhập: " + e.getMessage());
            }
        });
    }

    public void logout() {
        currentUser = null;
        prefs.edit()
                .remove(KEY_USER_ID)
                .remove(KEY_USERNAME)
                .putBoolean(KEY_IS_LOGGED_IN, false)
                .apply();
    }

    public boolean isLoggedIn() {
        return currentUser != null && prefs.getBoolean(KEY_IS_LOGGED_IN, false);
    }

    public User getCurrentUser() {
        return currentUser;
    }

    public int getCurrentUserId() {
        if (currentUser != null && currentUser.getId() != null) {
            try {
                return Integer.parseInt(currentUser.getId());
            } catch (NumberFormatException e) {
                Log.e(TAG, "Invalid user ID format", e);
            }
        }
        return -1;
    }

    private void setCurrentUser(User user) {
        this.currentUser = user;
        prefs.edit()
                .putString(KEY_USER_ID, user.getId())
                .putString(KEY_USERNAME, user.getUsername())
                .putBoolean(KEY_IS_LOGGED_IN, true)
                .apply();
    }

    private void loadCurrentUser() {
        if (prefs.getBoolean(KEY_IS_LOGGED_IN, false)) {
            String userId = prefs.getString(KEY_USER_ID, null);
            if (userId != null) {
                executor.execute(() -> {
                    try {
                        UserEntity userEntity = database.userDao().getUserByIdSync(Integer.parseInt(userId));
                        if (userEntity != null && userEntity.isActive()) {
                            currentUser = userEntity.toUser();
                        } else {
                            logout(); // User not found or inactive, logout
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error loading current user", e);
                        logout();
                    }
                });
            }
        }
    }

    private String hashPassword(String password) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(password.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "Error hashing password", e);
            return null;
        }
    }

    public void updateUserProfile(String fullName, String email, AuthCallback callback) {
        if (currentUser == null) {
            callback.onError("Người dùng chưa đăng nhập");
            return;
        }

        executor.execute(() -> {
            try {
                // Check if email is already used by another user
                UserEntity existingUser = database.userDao().getUserByEmail(email);
                if (existingUser != null && existingUser.getId() != getCurrentUserId()) {
                    callback.onError("Email đã được sử dụng bởi tài khoản khác");
                    return;
                }

                UserEntity userEntity = database.userDao().getUserByIdSync(getCurrentUserId());
                if (userEntity != null) {
                    userEntity.setFullName(fullName);
                    userEntity.setEmail(email);
                    database.userDao().updateUser(userEntity);

                    currentUser = userEntity.toUser();
                    callback.onSuccess(currentUser);
                } else {
                    callback.onError("Không tìm thấy thông tin người dùng");
                }
            } catch (Exception e) {
                Log.e(TAG, "Error updating profile", e);
                callback.onError("Lỗi cập nhật hồ sơ: " + e.getMessage());
            }
        });
    }
}

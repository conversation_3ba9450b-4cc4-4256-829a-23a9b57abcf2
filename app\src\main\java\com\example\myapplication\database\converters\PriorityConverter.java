package com.example.myapplication.database.converters;

import androidx.room.TypeConverter;
import com.example.myapplication.models.Task;

public class PriorityConverter {
    
    @TypeConverter
    public static String fromPriority(Task.Priority priority) {
        return priority == null ? null : priority.name();
    }

    @TypeConverter
    public static Task.Priority toPriority(String priority) {
        return priority == null ? null : Task.Priority.valueOf(priority);
    }
}

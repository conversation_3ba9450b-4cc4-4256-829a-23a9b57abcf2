// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CalendarView;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentCalendarBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CalendarView calendarView;

  @NonNull
  public final CheckBox cbShowIncompleteOnlyCalendar;

  @NonNull
  public final RecyclerView recyclerViewDayTasks;

  @NonNull
  public final TextView tvDateNumber;

  @NonNull
  public final TextView tvSelectedDate;

  @NonNull
  public final TextView tvTaskCount;

  private FragmentCalendarBinding(@NonNull LinearLayout rootView,
      @NonNull CalendarView calendarView, @NonNull CheckBox cbShowIncompleteOnlyCalendar,
      @NonNull RecyclerView recyclerViewDayTasks, @NonNull TextView tvDateNumber,
      @NonNull TextView tvSelectedDate, @NonNull TextView tvTaskCount) {
    this.rootView = rootView;
    this.calendarView = calendarView;
    this.cbShowIncompleteOnlyCalendar = cbShowIncompleteOnlyCalendar;
    this.recyclerViewDayTasks = recyclerViewDayTasks;
    this.tvDateNumber = tvDateNumber;
    this.tvSelectedDate = tvSelectedDate;
    this.tvTaskCount = tvTaskCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentCalendarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentCalendarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_calendar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentCalendarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.calendar_view;
      CalendarView calendarView = ViewBindings.findChildViewById(rootView, id);
      if (calendarView == null) {
        break missingId;
      }

      id = R.id.cb_show_incomplete_only_calendar;
      CheckBox cbShowIncompleteOnlyCalendar = ViewBindings.findChildViewById(rootView, id);
      if (cbShowIncompleteOnlyCalendar == null) {
        break missingId;
      }

      id = R.id.recycler_view_day_tasks;
      RecyclerView recyclerViewDayTasks = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewDayTasks == null) {
        break missingId;
      }

      id = R.id.tv_date_number;
      TextView tvDateNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvDateNumber == null) {
        break missingId;
      }

      id = R.id.tv_selected_date;
      TextView tvSelectedDate = ViewBindings.findChildViewById(rootView, id);
      if (tvSelectedDate == null) {
        break missingId;
      }

      id = R.id.tv_task_count;
      TextView tvTaskCount = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskCount == null) {
        break missingId;
      }

      return new FragmentCalendarBinding((LinearLayout) rootView, calendarView,
          cbShowIncompleteOnlyCalendar, recyclerViewDayTasks, tvDateNumber, tvSelectedDate,
          tvTaskCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

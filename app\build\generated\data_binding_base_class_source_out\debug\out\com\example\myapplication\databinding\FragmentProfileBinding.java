// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentProfileBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnLogin;

  @NonNull
  public final Button btnLogout;

  @NonNull
  public final ImageView ivUserAvatar;

  @NonNull
  public final LinearLayout layoutLoggedIn;

  @NonNull
  public final LinearLayout layoutNotLoggedIn;

  @NonNull
  public final LinearLayout menuAbout;

  @NonNull
  public final LinearLayout menuHelp;

  @NonNull
  public final LinearLayout menuSettings;

  @NonNull
  public final LinearLayout menuStatistics;

  @NonNull
  public final TextView tvUserEmail;

  @NonNull
  public final TextView tvUserInitials;

  @NonNull
  public final TextView tvUserName;

  private FragmentProfileBinding(@NonNull ScrollView rootView, @NonNull Button btnLogin,
      @NonNull Button btnLogout, @NonNull ImageView ivUserAvatar,
      @NonNull LinearLayout layoutLoggedIn, @NonNull LinearLayout layoutNotLoggedIn,
      @NonNull LinearLayout menuAbout, @NonNull LinearLayout menuHelp,
      @NonNull LinearLayout menuSettings, @NonNull LinearLayout menuStatistics,
      @NonNull TextView tvUserEmail, @NonNull TextView tvUserInitials,
      @NonNull TextView tvUserName) {
    this.rootView = rootView;
    this.btnLogin = btnLogin;
    this.btnLogout = btnLogout;
    this.ivUserAvatar = ivUserAvatar;
    this.layoutLoggedIn = layoutLoggedIn;
    this.layoutNotLoggedIn = layoutNotLoggedIn;
    this.menuAbout = menuAbout;
    this.menuHelp = menuHelp;
    this.menuSettings = menuSettings;
    this.menuStatistics = menuStatistics;
    this.tvUserEmail = tvUserEmail;
    this.tvUserInitials = tvUserInitials;
    this.tvUserName = tvUserName;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_login;
      Button btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.btn_logout;
      Button btnLogout = ViewBindings.findChildViewById(rootView, id);
      if (btnLogout == null) {
        break missingId;
      }

      id = R.id.iv_user_avatar;
      ImageView ivUserAvatar = ViewBindings.findChildViewById(rootView, id);
      if (ivUserAvatar == null) {
        break missingId;
      }

      id = R.id.layout_logged_in;
      LinearLayout layoutLoggedIn = ViewBindings.findChildViewById(rootView, id);
      if (layoutLoggedIn == null) {
        break missingId;
      }

      id = R.id.layout_not_logged_in;
      LinearLayout layoutNotLoggedIn = ViewBindings.findChildViewById(rootView, id);
      if (layoutNotLoggedIn == null) {
        break missingId;
      }

      id = R.id.menu_about;
      LinearLayout menuAbout = ViewBindings.findChildViewById(rootView, id);
      if (menuAbout == null) {
        break missingId;
      }

      id = R.id.menu_help;
      LinearLayout menuHelp = ViewBindings.findChildViewById(rootView, id);
      if (menuHelp == null) {
        break missingId;
      }

      id = R.id.menu_settings;
      LinearLayout menuSettings = ViewBindings.findChildViewById(rootView, id);
      if (menuSettings == null) {
        break missingId;
      }

      id = R.id.menu_statistics;
      LinearLayout menuStatistics = ViewBindings.findChildViewById(rootView, id);
      if (menuStatistics == null) {
        break missingId;
      }

      id = R.id.tv_user_email;
      TextView tvUserEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvUserEmail == null) {
        break missingId;
      }

      id = R.id.tv_user_initials;
      TextView tvUserInitials = ViewBindings.findChildViewById(rootView, id);
      if (tvUserInitials == null) {
        break missingId;
      }

      id = R.id.tv_user_name;
      TextView tvUserName = ViewBindings.findChildViewById(rootView, id);
      if (tvUserName == null) {
        break missingId;
      }

      return new FragmentProfileBinding((ScrollView) rootView, btnLogin, btnLogout, ivUserAvatar,
          layoutLoggedIn, layoutNotLoggedIn, menuAbout, menuHelp, menuSettings, menuStatistics,
          tvUserEmail, tvUserInitials, tvUserName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

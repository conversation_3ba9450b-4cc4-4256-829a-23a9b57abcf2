1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:6:22-76
13
14    <permission
14-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:8:5-49:19
21        android:allowBackup="true"
21-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:fullBackupContent="@xml/backup_rules"
25-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:11:9-54
26        android:icon="@mipmap/ic_launcher"
26-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:12:9-43
27        android:label="@string/app_name"
27-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:13:9-41
28        android:roundIcon="@mipmap/ic_launcher_round"
28-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:14:9-54
29        android:supportsRtl="true"
29-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:15:9-35
30        android:testOnly="true"
31        android:theme="@style/Theme.MyApplication" >
31-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:16:9-51
32        <activity
32-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:18:9-32:20
33            android:name="com.example.myapplication.MainActivity"
33-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:19:13-41
34            android:exported="true"
34-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:20:13-36
35            android:label="@string/app_name"
35-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:21:13-45
36            android:theme="@style/Theme.MyApplication.NoActionBar" >
36-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:22:13-67
37            <intent-filter>
37-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:23:13-27:29
38                <action android:name="android.intent.action.MAIN" />
38-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:24:17-69
38-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:24:25-66
39
40                <category android:name="android.intent.category.LAUNCHER" />
40-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:26:17-77
40-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:26:27-74
41            </intent-filter>
42
43            <meta-data
43-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:29:13-31:36
44                android:name="android.app.lib_name"
44-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:30:17-52
45                android:value="" />
45-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:31:17-33
46        </activity>
47        <activity
47-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:34:9-37:70
48            android:name="com.example.myapplication.activities.AuthActivity"
48-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:35:13-52
49            android:exported="false"
49-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:36:13-37
50            android:theme="@style/Theme.MyApplication.NoActionBar" />
50-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:37:13-67
51        <activity
51-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:39:9-48:20
52            android:name="com.example.myapplication.TaskDetailActivity"
52-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:40:13-47
53            android:exported="false"
53-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:41:13-37
54            android:label="Chi tiết Task"
54-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:42:13-42
55            android:parentActivityName="com.example.myapplication.MainActivity"
55-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:43:13-55
56            android:theme="@style/Theme.MyApplication.NoActionBar" >
56-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:44:13-67
57            <meta-data
57-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:45:13-47:49
58                android:name="android.support.PARENT_ACTIVITY"
58-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:46:17-63
59                android:value=".MainActivity" />
59-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:47:17-46
60        </activity>
61
62        <service
62-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
63            android:name="androidx.room.MultiInstanceInvalidationService"
63-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
64            android:directBootAware="true"
64-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
65            android:exported="false" />
65-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
66
67        <provider
67-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
68            android:name="androidx.startup.InitializationProvider"
68-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
69            android:authorities="com.example.myapplication.androidx-startup"
69-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
70            android:exported="false" >
70-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
71            <meta-data
71-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.emoji2.text.EmojiCompatInitializer"
72-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
73                android:value="androidx.startup" />
73-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
74            <meta-data
74-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
75-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
76                android:value="androidx.startup" />
76-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
79                android:value="androidx.startup" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
80        </provider>
81
82        <uses-library
82-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
83            android:name="androidx.window.extensions"
83-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
84            android:required="false" />
84-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
85        <uses-library
85-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
86            android:name="androidx.window.sidecar"
86-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
87            android:required="false" />
87-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
88
89        <receiver
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
90            android:name="androidx.profileinstaller.ProfileInstallReceiver"
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
91            android:directBootAware="false"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
92            android:enabled="true"
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
93            android:exported="true"
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
94            android:permission="android.permission.DUMP" >
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
95            <intent-filter>
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
96                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
97            </intent-filter>
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
99                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
100            </intent-filter>
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
102                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
103            </intent-filter>
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
105                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
106            </intent-filter>
107        </receiver>
108    </application>
109
110</manifest>

1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:6:22-76
13
14    <permission
14-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:8:5-49:19
21        android:allowBackup="true"
21-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:fullBackupContent="@xml/backup_rules"
25-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:11:9-54
26        android:icon="@mipmap/ic_launcher"
26-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:12:9-43
27        android:label="@string/app_name"
27-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:13:9-41
28        android:roundIcon="@mipmap/ic_launcher_round"
28-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:14:9-54
29        android:supportsRtl="true"
29-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:15:9-35
30        android:theme="@style/Theme.MyApplication" >
30-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:16:9-51
31        <activity
31-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:18:9-32:20
32            android:name="com.example.myapplication.MainActivity"
32-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:19:13-41
33            android:exported="true"
33-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:20:13-36
34            android:label="@string/app_name"
34-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:21:13-45
35            android:theme="@style/Theme.MyApplication.NoActionBar" >
35-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:22:13-67
36            <intent-filter>
36-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:23:13-27:29
37                <action android:name="android.intent.action.MAIN" />
37-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:24:17-69
37-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:24:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:26:17-77
39-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:26:27-74
40            </intent-filter>
41
42            <meta-data
42-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:29:13-31:36
43                android:name="android.app.lib_name"
43-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:30:17-52
44                android:value="" />
44-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:31:17-33
45        </activity>
46        <activity
46-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:34:9-37:70
47            android:name="com.example.myapplication.activities.AuthActivity"
47-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:35:13-52
48            android:exported="false"
48-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:36:13-37
49            android:theme="@style/Theme.MyApplication.NoActionBar" />
49-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:37:13-67
50        <activity
50-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:39:9-48:20
51            android:name="com.example.myapplication.TaskDetailActivity"
51-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:40:13-47
52            android:exported="false"
52-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:41:13-37
53            android:label="Chi tiết Task"
53-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:42:13-42
54            android:parentActivityName="com.example.myapplication.MainActivity"
54-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:43:13-55
55            android:theme="@style/Theme.MyApplication.NoActionBar" >
55-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:44:13-67
56            <meta-data
56-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:45:13-47:49
57                android:name="android.support.PARENT_ACTIVITY"
57-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:46:17-63
58                android:value=".MainActivity" />
58-->D:\TienDuong\DuyVC\app\src\main\AndroidManifest.xml:47:17-46
59        </activity>
60
61        <service
61-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
62            android:name="androidx.room.MultiInstanceInvalidationService"
62-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
63            android:directBootAware="true"
63-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
64            android:exported="false" />
64-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
65
66        <provider
66-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
67            android:name="androidx.startup.InitializationProvider"
67-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
68            android:authorities="com.example.myapplication.androidx-startup"
68-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
69            android:exported="false" >
69-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
70            <meta-data
70-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
71                android:name="androidx.emoji2.text.EmojiCompatInitializer"
71-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
72                android:value="androidx.startup" />
72-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
73            <meta-data
73-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
74-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
75                android:value="androidx.startup" />
75-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
76            <meta-data
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
78                android:value="androidx.startup" />
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
79        </provider>
80
81        <uses-library
81-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
82            android:name="androidx.window.extensions"
82-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
83            android:required="false" />
83-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
84        <uses-library
84-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
85            android:name="androidx.window.sidecar"
85-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
86            android:required="false" />
86-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
87
88        <receiver
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
89            android:name="androidx.profileinstaller.ProfileInstallReceiver"
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
90            android:directBootAware="false"
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
91            android:enabled="true"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
92            android:exported="true"
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
93            android:permission="android.permission.DUMP" >
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
95                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
98                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
99            </intent-filter>
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
101                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
102            </intent-filter>
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
104                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
105            </intent-filter>
106        </receiver>
107    </application>
108
109</manifest>

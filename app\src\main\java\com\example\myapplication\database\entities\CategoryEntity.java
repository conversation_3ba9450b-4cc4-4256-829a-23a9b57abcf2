package com.example.myapplication.database.entities;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;

import com.example.myapplication.models.Category;

@Entity(tableName = "categories")
public class CategoryEntity {
    
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    @ColumnInfo(name = "name")
    private String name;
    
    @ColumnInfo(name = "icon")
    private String icon;
    
    @ColumnInfo(name = "color")
    private String color;
    
    @ColumnInfo(name = "task_count")
    private int taskCount;

    @ColumnInfo(name = "user_id")
    private Integer userId; // Nullable for default categories

    // Constructors
    public CategoryEntity() {}

    public CategoryEntity(String name, String icon, String color) {
        this.name = name;
        this.icon = icon;
        this.color = color;
        this.taskCount = 0;
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public int getTaskCount() {
        return taskCount;
    }

    public void setTaskCount(int taskCount) {
        this.taskCount = taskCount;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    // Convert to Category model
    public Category toCategory() {
        Category category = new Category(String.valueOf(this.id), this.name, this.color, this.icon);
        category.setTaskCount(this.taskCount);
        return category;
    }

    // Create from Category model
    public static CategoryEntity fromCategory(Category category) {
        CategoryEntity entity = new CategoryEntity();
        if (category.getId() != null && !category.getId().isEmpty()) {
            try {
                entity.setId(Integer.parseInt(category.getId()));
            } catch (NumberFormatException e) {
                // ID will be auto-generated
            }
        }
        entity.setName(category.getName());
        entity.setIcon(category.getIcon());
        entity.setColor(category.getColor());
        entity.setTaskCount(category.getTaskCount());
        return entity;
    }
}

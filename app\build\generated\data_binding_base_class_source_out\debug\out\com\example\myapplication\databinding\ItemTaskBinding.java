// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final CheckBox cbTaskCompleted;

  @NonNull
  public final TextView tvTaskCategory;

  @NonNull
  public final TextView tvTaskDescription;

  @NonNull
  public final TextView tvTaskDueDate;

  @NonNull
  public final TextView tvTaskPriority;

  @NonNull
  public final TextView tvTaskTitle;

  @NonNull
  public final View viewPriorityIndicator;

  private ItemTaskBinding(@NonNull MaterialCardView rootView, @NonNull CheckBox cbTaskCompleted,
      @NonNull TextView tvTaskCategory, @NonNull TextView tvTaskDescription,
      @NonNull TextView tvTaskDueDate, @NonNull TextView tvTaskPriority,
      @NonNull TextView tvTaskTitle, @NonNull View viewPriorityIndicator) {
    this.rootView = rootView;
    this.cbTaskCompleted = cbTaskCompleted;
    this.tvTaskCategory = tvTaskCategory;
    this.tvTaskDescription = tvTaskDescription;
    this.tvTaskDueDate = tvTaskDueDate;
    this.tvTaskPriority = tvTaskPriority;
    this.tvTaskTitle = tvTaskTitle;
    this.viewPriorityIndicator = viewPriorityIndicator;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cb_task_completed;
      CheckBox cbTaskCompleted = ViewBindings.findChildViewById(rootView, id);
      if (cbTaskCompleted == null) {
        break missingId;
      }

      id = R.id.tv_task_category;
      TextView tvTaskCategory = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskCategory == null) {
        break missingId;
      }

      id = R.id.tv_task_description;
      TextView tvTaskDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskDescription == null) {
        break missingId;
      }

      id = R.id.tv_task_due_date;
      TextView tvTaskDueDate = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskDueDate == null) {
        break missingId;
      }

      id = R.id.tv_task_priority;
      TextView tvTaskPriority = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskPriority == null) {
        break missingId;
      }

      id = R.id.tv_task_title;
      TextView tvTaskTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskTitle == null) {
        break missingId;
      }

      id = R.id.view_priority_indicator;
      View viewPriorityIndicator = ViewBindings.findChildViewById(rootView, id);
      if (viewPriorityIndicator == null) {
        break missingId;
      }

      return new ItemTaskBinding((MaterialCardView) rootView, cbTaskCompleted, tvTaskCategory,
          tvTaskDescription, tvTaskDueDate, tvTaskPriority, tvTaskTitle, viewPriorityIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddCategoryBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnIconFamily;

  @NonNull
  public final Button btnIconFinance;

  @NonNull
  public final Button btnIconFood;

  @NonNull
  public final Button btnIconHealth;

  @NonNull
  public final Button btnIconHobby;

  @NonNull
  public final Button btnIconHome;

  @NonNull
  public final Button btnIconOther;

  @NonNull
  public final Button btnIconPersonal;

  @NonNull
  public final Button btnIconShopping;

  @NonNull
  public final Button btnIconStudy;

  @NonNull
  public final Button btnIconTravel;

  @NonNull
  public final Button btnIconWork;

  @NonNull
  public final View colorBlue;

  @NonNull
  public final View colorGreen;

  @NonNull
  public final View colorOrange;

  @NonNull
  public final View colorPink;

  @NonNull
  public final View colorPurple;

  @NonNull
  public final View colorRed;

  @NonNull
  public final TextInputEditText etCategoryName;

  private DialogAddCategoryBinding(@NonNull LinearLayout rootView, @NonNull Button btnIconFamily,
      @NonNull Button btnIconFinance, @NonNull Button btnIconFood, @NonNull Button btnIconHealth,
      @NonNull Button btnIconHobby, @NonNull Button btnIconHome, @NonNull Button btnIconOther,
      @NonNull Button btnIconPersonal, @NonNull Button btnIconShopping,
      @NonNull Button btnIconStudy, @NonNull Button btnIconTravel, @NonNull Button btnIconWork,
      @NonNull View colorBlue, @NonNull View colorGreen, @NonNull View colorOrange,
      @NonNull View colorPink, @NonNull View colorPurple, @NonNull View colorRed,
      @NonNull TextInputEditText etCategoryName) {
    this.rootView = rootView;
    this.btnIconFamily = btnIconFamily;
    this.btnIconFinance = btnIconFinance;
    this.btnIconFood = btnIconFood;
    this.btnIconHealth = btnIconHealth;
    this.btnIconHobby = btnIconHobby;
    this.btnIconHome = btnIconHome;
    this.btnIconOther = btnIconOther;
    this.btnIconPersonal = btnIconPersonal;
    this.btnIconShopping = btnIconShopping;
    this.btnIconStudy = btnIconStudy;
    this.btnIconTravel = btnIconTravel;
    this.btnIconWork = btnIconWork;
    this.colorBlue = colorBlue;
    this.colorGreen = colorGreen;
    this.colorOrange = colorOrange;
    this.colorPink = colorPink;
    this.colorPurple = colorPurple;
    this.colorRed = colorRed;
    this.etCategoryName = etCategoryName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddCategoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddCategoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_category, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddCategoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_icon_family;
      Button btnIconFamily = ViewBindings.findChildViewById(rootView, id);
      if (btnIconFamily == null) {
        break missingId;
      }

      id = R.id.btn_icon_finance;
      Button btnIconFinance = ViewBindings.findChildViewById(rootView, id);
      if (btnIconFinance == null) {
        break missingId;
      }

      id = R.id.btn_icon_food;
      Button btnIconFood = ViewBindings.findChildViewById(rootView, id);
      if (btnIconFood == null) {
        break missingId;
      }

      id = R.id.btn_icon_health;
      Button btnIconHealth = ViewBindings.findChildViewById(rootView, id);
      if (btnIconHealth == null) {
        break missingId;
      }

      id = R.id.btn_icon_hobby;
      Button btnIconHobby = ViewBindings.findChildViewById(rootView, id);
      if (btnIconHobby == null) {
        break missingId;
      }

      id = R.id.btn_icon_home;
      Button btnIconHome = ViewBindings.findChildViewById(rootView, id);
      if (btnIconHome == null) {
        break missingId;
      }

      id = R.id.btn_icon_other;
      Button btnIconOther = ViewBindings.findChildViewById(rootView, id);
      if (btnIconOther == null) {
        break missingId;
      }

      id = R.id.btn_icon_personal;
      Button btnIconPersonal = ViewBindings.findChildViewById(rootView, id);
      if (btnIconPersonal == null) {
        break missingId;
      }

      id = R.id.btn_icon_shopping;
      Button btnIconShopping = ViewBindings.findChildViewById(rootView, id);
      if (btnIconShopping == null) {
        break missingId;
      }

      id = R.id.btn_icon_study;
      Button btnIconStudy = ViewBindings.findChildViewById(rootView, id);
      if (btnIconStudy == null) {
        break missingId;
      }

      id = R.id.btn_icon_travel;
      Button btnIconTravel = ViewBindings.findChildViewById(rootView, id);
      if (btnIconTravel == null) {
        break missingId;
      }

      id = R.id.btn_icon_work;
      Button btnIconWork = ViewBindings.findChildViewById(rootView, id);
      if (btnIconWork == null) {
        break missingId;
      }

      id = R.id.color_blue;
      View colorBlue = ViewBindings.findChildViewById(rootView, id);
      if (colorBlue == null) {
        break missingId;
      }

      id = R.id.color_green;
      View colorGreen = ViewBindings.findChildViewById(rootView, id);
      if (colorGreen == null) {
        break missingId;
      }

      id = R.id.color_orange;
      View colorOrange = ViewBindings.findChildViewById(rootView, id);
      if (colorOrange == null) {
        break missingId;
      }

      id = R.id.color_pink;
      View colorPink = ViewBindings.findChildViewById(rootView, id);
      if (colorPink == null) {
        break missingId;
      }

      id = R.id.color_purple;
      View colorPurple = ViewBindings.findChildViewById(rootView, id);
      if (colorPurple == null) {
        break missingId;
      }

      id = R.id.color_red;
      View colorRed = ViewBindings.findChildViewById(rootView, id);
      if (colorRed == null) {
        break missingId;
      }

      id = R.id.et_category_name;
      TextInputEditText etCategoryName = ViewBindings.findChildViewById(rootView, id);
      if (etCategoryName == null) {
        break missingId;
      }

      return new DialogAddCategoryBinding((LinearLayout) rootView, btnIconFamily, btnIconFinance,
          btnIconFood, btnIconHealth, btnIconHobby, btnIconHome, btnIconOther, btnIconPersonal,
          btnIconShopping, btnIconStudy, btnIconTravel, btnIconWork, colorBlue, colorGreen,
          colorOrange, colorPink, colorPurple, colorRed, etCategoryName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

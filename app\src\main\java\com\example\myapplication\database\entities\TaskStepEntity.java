package com.example.myapplication.database.entities;

import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.PrimaryKey;
import androidx.room.Index;

import com.example.myapplication.models.TaskStep;

import java.util.Date;

@Entity(tableName = "task_steps",
        foreignKeys = @ForeignKey(entity = TaskEntity.class,
                                 parentColumns = "id",
                                 childColumns = "taskId",
                                 onDelete = ForeignKey.CASCADE),
        indices = {@Index("taskId")})
public class TaskStepEntity {
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    private int taskId; // Foreign key to TaskEntity
    private String title;
    private String description;
    private boolean completed;
    private Date createdAt;
    private Date completedAt;
    private int stepOrder; // Thứ tự của step

    // Constructors
    public TaskStepEntity() {}

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public int getTaskId() { return taskId; }
    public void setTaskId(int taskId) { this.taskId = taskId; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public boolean isCompleted() { return completed; }
    public void setCompleted(boolean completed) { this.completed = completed; }

    public Date getCreatedAt() { return createdAt; }
    public void setCreatedAt(Date createdAt) { this.createdAt = createdAt; }

    public Date getCompletedAt() { return completedAt; }
    public void setCompletedAt(Date completedAt) { this.completedAt = completedAt; }

    public int getStepOrder() { return stepOrder; }
    public void setStepOrder(int stepOrder) { this.stepOrder = stepOrder; }

    // Convert to TaskStep model
    public TaskStep toTaskStep() {
        TaskStep step = new TaskStep();
        step.setId(String.valueOf(this.id));
        step.setTitle(this.title);
        step.setDescription(this.description);
        step.setCompleted(this.completed);
        step.setCreatedAt(this.createdAt);
        step.setCompletedAt(this.completedAt);
        step.setOrder(this.stepOrder);
        return step;
    }

    // Create from TaskStep model
    public static TaskStepEntity fromTaskStep(TaskStep step, int taskId) {
        TaskStepEntity entity = new TaskStepEntity();
        if (step.getId() != null && !step.getId().isEmpty()) {
            try {
                entity.setId(Integer.parseInt(step.getId()));
            } catch (NumberFormatException e) {
                // ID will be auto-generated
            }
        }
        entity.setTaskId(taskId);
        entity.setTitle(step.getTitle());
        entity.setDescription(step.getDescription());
        entity.setCompleted(step.isCompleted());
        entity.setCreatedAt(step.getCreatedAt());
        entity.setCompletedAt(step.getCompletedAt());
        entity.setStepOrder(step.getOrder());
        return entity;
    }
}

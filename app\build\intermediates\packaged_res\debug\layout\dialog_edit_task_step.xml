<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Title Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Tiêu đề bước">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etStepTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="2" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Description Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="<PERSON><PERSON> tả bước (tùy ch<PERSON>n)">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etStepDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:maxLines="4"
            android:minLines="2" />

    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout>

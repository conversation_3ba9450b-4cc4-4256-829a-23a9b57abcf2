package com.example.myapplication.viewmodel;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.example.myapplication.database.entities.CategoryEntity;
import com.example.myapplication.database.entities.TaskEntity;
import com.example.myapplication.database.entities.TaskStepEntity;
import com.example.myapplication.models.Task;
import com.example.myapplication.repository.TodoRepository;

import java.util.Date;
import java.util.List;

public class TodoViewModel extends AndroidViewModel {

    private TodoRepository repository;
    
    // LiveData for UI
    private LiveData<List<TaskEntity>> allTasks;
    private LiveData<List<CategoryEntity>> allCategories;
    private MutableLiveData<Integer> selectedCategoryId = new MutableLiveData<>();
    private MutableLiveData<String> searchQuery = new MutableLiveData<>();
    private MutableLiveData<Date> selectedDate = new MutableLiveData<>();

    public TodoViewModel(@NonNull Application application) {
        super(application);
        repository = new TodoRepository(application);
        allTasks = repository.getAllTasks();
        allCategories = repository.getCategoriesWithTaskCount();
    }

    public TodoRepository getRepository() {
        return repository;
    }

    // Task operations
    public LiveData<List<TaskEntity>> getAllTasks() {
        return allTasks;
    }

    public LiveData<TaskEntity> getTaskById(int taskId) {
        return repository.getTaskById(taskId);
    }

    public LiveData<List<TaskEntity>> getTasksByUser(int userId) {
        return repository.getTasksByUser(userId);
    }

    public LiveData<List<TaskEntity>> getFilteredTasks() {
        return Transformations.switchMap(selectedCategoryId, categoryId -> {
            if (categoryId == null || categoryId == -1) {
                return repository.getAllTasks();
            } else {
                return repository.getTasksByCategory(categoryId);
            }
        });
    }

    public LiveData<List<TaskEntity>> getSearchResults() {
        return Transformations.switchMap(searchQuery, query -> {
            if (query == null || query.trim().isEmpty()) {
                return repository.getAllTasks();
            } else {
                return repository.searchTasks(query);
            }
        });
    }

    public LiveData<List<TaskEntity>> getTasksByDate() {
        return Transformations.switchMap(selectedDate, date -> {
            if (date == null) {
                return repository.getAllTasks();
            } else {
                return repository.getTasksByDate(date);
            }
        });
    }

    public LiveData<List<TaskEntity>> getTodayTasks() {
        return repository.getTodayTasks();
    }

    public LiveData<List<TaskEntity>> getOverdueTasks() {
        return repository.getOverdueTasks();
    }

    public LiveData<List<TaskEntity>> getTasksByPriorityAndDate() {
        return repository.getTasksByPriorityAndDate();
    }

    public void insertTask(TaskEntity task) {
        repository.insertTask(task);
    }

    public void insertTask(Task task, int categoryId) {
        TaskEntity entity = repository.convertTaskToEntity(task, categoryId);
        repository.insertTask(entity);
    }

    public void updateTask(TaskEntity task) {
        repository.updateTask(task);
    }

    public void updateTaskCompletion(int taskId, boolean isCompleted) {
        repository.updateTaskCompletion(taskId, isCompleted);
    }

    public void deleteTask(TaskEntity task) {
        repository.deleteTask(task);
    }

    public void deleteTaskById(int taskId) {
        repository.deleteTaskById(taskId);
    }

    // Category operations
    public LiveData<List<CategoryEntity>> getAllCategories() {
        return allCategories;
    }

    public LiveData<List<CategoryEntity>> getCategoriesForUser(int userId) {
        return repository.getCategoriesForUser(userId);
    }

    public LiveData<CategoryEntity> getCategoryById(int categoryId) {
        return repository.getCategoryById(categoryId);
    }

    public void insertCategory(CategoryEntity category) {
        repository.insertCategory(category);
    }

    public void updateCategory(CategoryEntity category) {
        repository.updateCategory(category);
    }

    public void deleteCategory(CategoryEntity category) {
        repository.deleteCategory(category);
    }

    // Filter and search operations
    public void setSelectedCategoryId(Integer categoryId) {
        selectedCategoryId.setValue(categoryId);
    }

    public void setSearchQuery(String query) {
        searchQuery.setValue(query);
    }

    public void setSelectedDate(Date date) {
        selectedDate.setValue(date);
    }

    public MutableLiveData<Integer> getSelectedCategoryId() {
        return selectedCategoryId;
    }

    public MutableLiveData<String> getSearchQuery() {
        return searchQuery;
    }

    public MutableLiveData<Date> getSelectedDate() {
        return selectedDate;
    }

    // Statistics
    public LiveData<Integer> getTotalTaskCount() {
        return repository.getTotalTaskCount();
    }

    public LiveData<Integer> getCompletedTaskCount() {
        return repository.getCompletedTaskCount();
    }

    public LiveData<Integer> getPendingTaskCount() {
        return repository.getPendingTaskCount();
    }

    public LiveData<Integer> getOverdueTaskCount() {
        return repository.getOverdueTaskCount();
    }

    public LiveData<Integer> getTaskCountByCategory(int categoryId) {
        return repository.getTaskCountByCategory(categoryId);
    }

    // TaskStep operations
    public LiveData<List<TaskStepEntity>> getStepsByTaskId(int taskId) {
        return repository.getStepsByTaskId(taskId);
    }

    public void insertTaskStep(TaskStepEntity step) {
        repository.insertTaskStep(step);
    }

    public void insertTaskSteps(List<TaskStepEntity> steps) {
        repository.insertTaskSteps(steps);
    }

    public void updateTaskStep(TaskStepEntity step) {
        repository.updateTaskStep(step);
    }

    public void deleteTaskStep(TaskStepEntity step) {
        repository.deleteTaskStep(step);
    }

    public void deleteStepsByTaskId(int taskId) {
        repository.deleteStepsByTaskId(taskId);
    }
}

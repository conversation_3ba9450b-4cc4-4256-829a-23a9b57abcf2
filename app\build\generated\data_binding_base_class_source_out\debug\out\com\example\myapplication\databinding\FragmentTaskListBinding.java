// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTaskListBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button btnAddCategory;

  @NonNull
  public final CheckBox cbShowIncompleteOnly;

  @NonNull
  public final EditText etSearch;

  @NonNull
  public final FloatingActionButton fabAddTask;

  @NonNull
  public final RecyclerView recyclerViewTasks;

  @NonNull
  public final Spinner spinnerCategoryFilter;

  private FragmentTaskListBinding(@NonNull CoordinatorLayout rootView,
      @NonNull Button btnAddCategory, @NonNull CheckBox cbShowIncompleteOnly,
      @NonNull EditText etSearch, @NonNull FloatingActionButton fabAddTask,
      @NonNull RecyclerView recyclerViewTasks, @NonNull Spinner spinnerCategoryFilter) {
    this.rootView = rootView;
    this.btnAddCategory = btnAddCategory;
    this.cbShowIncompleteOnly = cbShowIncompleteOnly;
    this.etSearch = etSearch;
    this.fabAddTask = fabAddTask;
    this.recyclerViewTasks = recyclerViewTasks;
    this.spinnerCategoryFilter = spinnerCategoryFilter;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTaskListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTaskListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_task_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTaskListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_add_category;
      Button btnAddCategory = ViewBindings.findChildViewById(rootView, id);
      if (btnAddCategory == null) {
        break missingId;
      }

      id = R.id.cb_show_incomplete_only;
      CheckBox cbShowIncompleteOnly = ViewBindings.findChildViewById(rootView, id);
      if (cbShowIncompleteOnly == null) {
        break missingId;
      }

      id = R.id.et_search;
      EditText etSearch = ViewBindings.findChildViewById(rootView, id);
      if (etSearch == null) {
        break missingId;
      }

      id = R.id.fab_add_task;
      FloatingActionButton fabAddTask = ViewBindings.findChildViewById(rootView, id);
      if (fabAddTask == null) {
        break missingId;
      }

      id = R.id.recycler_view_tasks;
      RecyclerView recyclerViewTasks = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewTasks == null) {
        break missingId;
      }

      id = R.id.spinner_category_filter;
      Spinner spinnerCategoryFilter = ViewBindings.findChildViewById(rootView, id);
      if (spinnerCategoryFilter == null) {
        break missingId;
      }

      return new FragmentTaskListBinding((CoordinatorLayout) rootView, btnAddCategory,
          cbShowIncompleteOnly, etSearch, fabAddTask, recyclerViewTasks, spinnerCategoryFilter);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

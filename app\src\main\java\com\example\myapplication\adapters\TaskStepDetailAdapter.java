package com.example.myapplication.adapters;

import android.graphics.Paint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapplication.R;
import com.example.myapplication.models.TaskStep;

import java.util.List;

public class TaskStepDetailAdapter extends RecyclerView.Adapter<TaskStepDetailAdapter.StepViewHolder> {

    private List<TaskStep> stepList;
    private OnStepActionListener listener;

    public interface OnStepActionListener {
        void onStepCompletionChanged(int position, TaskStep step, boolean isCompleted);
        void onStepEditClicked(int position, TaskStep step);
        void onStepDeleteClicked(int position, TaskStep step);
    }

    public TaskStepDetailAdapter(List<TaskStep> stepList, OnStepActionListener listener) {
        this.stepList = stepList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public StepViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_task_step_detail, parent, false);
        return new StepViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull StepViewHolder holder, int position) {
        TaskStep step = stepList.get(position);
        holder.bind(step, position);
    }

    @Override
    public int getItemCount() {
        return stepList.size();
    }

    public void updateSteps(List<TaskStep> newSteps) {
        this.stepList = newSteps;
        notifyDataSetChanged();
    }

    public class StepViewHolder extends RecyclerView.ViewHolder {
        private CheckBox cbStepCompleted;
        private TextView tvStepTitle;
        private TextView tvStepDescription;
        private TextView tvStepStatus;
        private ImageButton btnEditStep;
        private ImageButton btnDeleteStep;

        public StepViewHolder(@NonNull View itemView) {
            super(itemView);
            cbStepCompleted = itemView.findViewById(R.id.cb_step_completed);
            tvStepTitle = itemView.findViewById(R.id.tv_step_title);
            tvStepDescription = itemView.findViewById(R.id.tv_step_description);
            tvStepStatus = itemView.findViewById(R.id.tv_step_status);
            btnEditStep = itemView.findViewById(R.id.btn_edit_step);
            btnDeleteStep = itemView.findViewById(R.id.btn_delete_step_detail);
        }

        public void bind(TaskStep step, int position) {
            // Set step title
            tvStepTitle.setText(step.getTitle());
            
            // Set step description
            if (step.getDescription() != null && !step.getDescription().trim().isEmpty()) {
                tvStepDescription.setText(step.getDescription());
                tvStepDescription.setVisibility(View.VISIBLE);
            } else {
                tvStepDescription.setVisibility(View.GONE);
            }

            // Set completion status
            cbStepCompleted.setChecked(step.isCompleted());
            
            // Update UI based on completion status
            updateStepAppearance(step.isCompleted());
            
            // Set status text
            tvStepStatus.setText(step.isCompleted() ? "Đã hoàn thành" : "Chưa hoàn thành");
            
            // Checkbox change listener
            cbStepCompleted.setOnCheckedChangeListener((buttonView, isChecked) -> {
                step.setCompleted(isChecked);
                updateStepAppearance(isChecked);
                tvStepStatus.setText(isChecked ? "Đã hoàn thành" : "Chưa hoàn thành");
                
                if (listener != null) {
                    listener.onStepCompletionChanged(position, step, isChecked);
                }
            });

            // Edit button click
            btnEditStep.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onStepEditClicked(position, step);
                }
            });

            // Delete button click
            btnDeleteStep.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onStepDeleteClicked(position, step);
                }
            });
        }

        private void updateStepAppearance(boolean isCompleted) {
            if (isCompleted) {
                // Strike through text for completed steps
                tvStepTitle.setPaintFlags(tvStepTitle.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                tvStepTitle.setAlpha(0.6f);
                tvStepDescription.setPaintFlags(tvStepDescription.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                tvStepDescription.setAlpha(0.6f);
            } else {
                // Remove strike through for incomplete steps
                tvStepTitle.setPaintFlags(tvStepTitle.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                tvStepTitle.setAlpha(1.0f);
                tvStepDescription.setPaintFlags(tvStepDescription.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                tvStepDescription.setAlpha(1.0f);
            }
        }
    }
}

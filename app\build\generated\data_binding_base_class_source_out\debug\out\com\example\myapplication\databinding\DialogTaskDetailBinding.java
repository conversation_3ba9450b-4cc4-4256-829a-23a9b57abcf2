// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import com.google.android.material.chip.Chip;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogTaskDetailBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnAddStepDetail;

  @NonNull
  public final Chip chipCategory;

  @NonNull
  public final Chip chipPriority;

  @NonNull
  public final LinearLayout layoutProgress;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewStepsDetail;

  @NonNull
  public final TextView tvNoSteps;

  @NonNull
  public final TextView tvProgressText;

  @NonNull
  public final TextView tvTaskDescription;

  @NonNull
  public final TextView tvTaskTitle;

  private DialogTaskDetailBinding(@NonNull ScrollView rootView, @NonNull Button btnAddStepDetail,
      @NonNull Chip chipCategory, @NonNull Chip chipPriority, @NonNull LinearLayout layoutProgress,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView recyclerViewStepsDetail,
      @NonNull TextView tvNoSteps, @NonNull TextView tvProgressText,
      @NonNull TextView tvTaskDescription, @NonNull TextView tvTaskTitle) {
    this.rootView = rootView;
    this.btnAddStepDetail = btnAddStepDetail;
    this.chipCategory = chipCategory;
    this.chipPriority = chipPriority;
    this.layoutProgress = layoutProgress;
    this.progressBar = progressBar;
    this.recyclerViewStepsDetail = recyclerViewStepsDetail;
    this.tvNoSteps = tvNoSteps;
    this.tvProgressText = tvProgressText;
    this.tvTaskDescription = tvTaskDescription;
    this.tvTaskTitle = tvTaskTitle;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogTaskDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogTaskDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_task_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogTaskDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_add_step_detail;
      Button btnAddStepDetail = ViewBindings.findChildViewById(rootView, id);
      if (btnAddStepDetail == null) {
        break missingId;
      }

      id = R.id.chip_category;
      Chip chipCategory = ViewBindings.findChildViewById(rootView, id);
      if (chipCategory == null) {
        break missingId;
      }

      id = R.id.chip_priority;
      Chip chipPriority = ViewBindings.findChildViewById(rootView, id);
      if (chipPriority == null) {
        break missingId;
      }

      id = R.id.layout_progress;
      LinearLayout layoutProgress = ViewBindings.findChildViewById(rootView, id);
      if (layoutProgress == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recycler_view_steps_detail;
      RecyclerView recyclerViewStepsDetail = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewStepsDetail == null) {
        break missingId;
      }

      id = R.id.tv_no_steps;
      TextView tvNoSteps = ViewBindings.findChildViewById(rootView, id);
      if (tvNoSteps == null) {
        break missingId;
      }

      id = R.id.tv_progress_text;
      TextView tvProgressText = ViewBindings.findChildViewById(rootView, id);
      if (tvProgressText == null) {
        break missingId;
      }

      id = R.id.tv_task_description;
      TextView tvTaskDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskDescription == null) {
        break missingId;
      }

      id = R.id.tv_task_title;
      TextView tvTaskTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskTitle == null) {
        break missingId;
      }

      return new DialogTaskDetailBinding((ScrollView) rootView, btnAddStepDetail, chipCategory,
          chipPriority, layoutProgress, progressBar, recyclerViewStepsDetail, tvNoSteps,
          tvProgressText, tvTaskDescription, tvTaskTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

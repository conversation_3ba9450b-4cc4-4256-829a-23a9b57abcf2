package com.example.myapplication.adapters;

import android.graphics.Paint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapplication.R;
import com.example.myapplication.models.Task;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

public class TaskAdapter extends RecyclerView.Adapter<TaskAdapter.TaskViewHolder> {

    private List<Task> taskList;
    private OnTaskClickListener onTaskClickListener;

    public TaskAdapter(List<Task> taskList) {
        this.taskList = taskList;
    }

    public interface OnTaskClickListener {
        void onTaskClick(Task task);
        void onTaskToggle(Task task, boolean isCompleted);
    }

    public void setOnTaskClickListener(OnTaskClickListener listener) {
        this.onTaskClickListener = listener;
    }

    @NonNull
    @Override
    public TaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_task, parent, false);
        return new TaskViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TaskViewHolder holder, int position) {
        Task task = taskList.get(position);
        
        holder.titleTextView.setText(task.getTitle());
        holder.descriptionTextView.setText(task.getDescription());

        // Handle null category
        if (task.getCategory() != null) {
            holder.categoryTextView.setText(task.getCategory().getName());
        } else {
            holder.categoryTextView.setText("Không có danh mục");
        }

        holder.priorityTextView.setText(task.getPriority().getDisplayName());

        // Clear listener before setting checked state to avoid unwanted triggers
        holder.checkBox.setOnCheckedChangeListener(null);
        holder.checkBox.setChecked(task.isCompleted());
        
        // Format and set due date
        if (task.getDueDate() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
            holder.dueDateTextView.setText("📅 " + sdf.format(task.getDueDate()));
        } else {
            holder.dueDateTextView.setText("");
        }
        
        // Set priority color
        holder.priorityIndicator.setBackgroundColor(android.graphics.Color.parseColor(task.getPriority().getColor()));
        
        // Set category color
        holder.categoryTextView.setBackgroundColor(task.getCategory().getColorInt());
        
        // Strike through text if completed
        if (task.isCompleted()) {
            holder.titleTextView.setPaintFlags(holder.titleTextView.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
            holder.descriptionTextView.setPaintFlags(holder.descriptionTextView.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
            holder.itemView.setAlpha(0.6f);
        } else {
            holder.titleTextView.setPaintFlags(holder.titleTextView.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
            holder.descriptionTextView.setPaintFlags(holder.descriptionTextView.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
            holder.itemView.setAlpha(1.0f);
        }
        
        // Set overdue styling
        if (task.isOverdue()) {
            holder.dueDateTextView.setTextColor(android.graphics.Color.parseColor("#F44336"));
        } else {
            holder.dueDateTextView.setTextColor(android.graphics.Color.parseColor("#666666"));
        }
        
        // Click listeners
        holder.itemView.setOnClickListener(v -> {
            if (onTaskClickListener != null) {
                onTaskClickListener.onTaskClick(task);
            }
        });
        
        // Set checkbox listener
        holder.checkBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // Only update if the state actually changed
            if (task.isCompleted() != isChecked) {
                // Don't update task.setCompleted() here - let the Fragment handle it
                // Just notify the listener
                if (onTaskClickListener != null) {
                    onTaskClickListener.onTaskToggle(task, isChecked);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return taskList.size();
    }

    public void updateTasks(List<Task> newTasks) {
        this.taskList = newTasks;
        notifyDataSetChanged();
    }

    public void updateTask(Task updatedTask) {
        for (int i = 0; i < taskList.size(); i++) {
            if (taskList.get(i).getId().equals(updatedTask.getId())) {
                taskList.set(i, updatedTask);
                notifyItemChanged(i);
                break;
            }
        }
    }

    public static class TaskViewHolder extends RecyclerView.ViewHolder {
        TextView titleTextView, descriptionTextView, categoryTextView, dueDateTextView, priorityTextView;
        CheckBox checkBox;
        View priorityIndicator;

        public TaskViewHolder(@NonNull View itemView) {
            super(itemView);
            titleTextView = itemView.findViewById(R.id.tv_task_title);
            descriptionTextView = itemView.findViewById(R.id.tv_task_description);
            categoryTextView = itemView.findViewById(R.id.tv_task_category);
            dueDateTextView = itemView.findViewById(R.id.tv_task_due_date);
            priorityTextView = itemView.findViewById(R.id.tv_task_priority);
            checkBox = itemView.findViewById(R.id.cb_task_completed);
            priorityIndicator = itemView.findViewById(R.id.view_priority_indicator);
        }
    }
}
